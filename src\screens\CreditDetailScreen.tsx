import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { getReports } from '../config/api';
import { formatCurrency, formatTurkishDateTime } from '../utils/dateUtils';

type NavigationProp = StackNavigationProp<RootStackParamList, 'CreditDetail'>;
type RoutePropType = RouteProp<RootStackParamList, 'CreditDetail'>;

const CreditDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RoutePropType>();
  const [reportData, setReportData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async (): Promise<void> => {
    try {
      setLoading(true);
      const { reportType, startDate, endDate } = route.params;

      let response;
      if (reportType === 'custom' && startDate && endDate) {
        response = await getReports(reportType, startDate, endDate);
      } else {
        response = await getReports(reportType);
      }

      setReportData(response);
    } catch (error) {
      console.error('Error fetching report data:', error);
      setReportData(null);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async (): Promise<void> => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Ödenmez Detayları</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Ödenmez Detayları</Text>
      </View>

      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Icon name="credit-card" size={24} color="#607D8B" />
          <Text style={styles.summaryLabel}>Toplam Ödenmez</Text>
          <Text style={styles.summaryValue}>
            {formatCurrency(
              reportData?.creditTotal && typeof (reportData.creditTotal as any).total === 'string'
                ? parseFloat((reportData.creditTotal as any).total)
                : (reportData.creditTotal as any)?.total || 0
            )}
          </Text>
        </View>
        <View style={styles.summaryCard}>
          <Icon name="list-alt" size={24} color="#795548" />
          <Text style={styles.summaryLabel}>İşlem Sayısı</Text>
          <Text style={styles.summaryValue}>
            {reportData?.creditTotal && (reportData.creditTotal as any).details
              ? (reportData.creditTotal as any).details.length
              : 0
            }
          </Text>
        </View>
      </View>

      <ScrollView
        style={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {reportData?.creditTotal && (reportData.creditTotal as any).details && (reportData.creditTotal as any).details.length > 0 ? (
          <View style={styles.sectionContainer}>
            {(reportData.creditTotal as any).details.map((item: any, index: number) => (
              <View key={index} style={styles.orderItem}>
                <View style={styles.orderHeader}>
                  <Text style={styles.orderNumber}>#{item.order_id || 'N/A'}</Text>
                  <Text style={styles.orderTotal}>{formatCurrency(parseFloat(item.amount || item.total || 0))}</Text>
                </View>

                <View style={styles.creditInfo}>
                  <Icon name="credit-card" size={16} color="#607D8B" />
                  <Text style={styles.creditText}>Ödenmez İşlemi</Text>
                </View>

                {/* Sipariş Detayları */}
                <View style={styles.orderDetailsContainer}>
                  {item.order_date && (
                    <View style={styles.detailRow}>
                      <Icon name="event" size={16} color="#666" />
                      <Text style={styles.detailLabel}>Sipariş Tarihi:</Text>
                      <Text style={styles.detailValue}>{formatTurkishDateTime(item.order_date)}</Text>
                    </View>
                  )}

                  {item.table_title && (
                    <View style={styles.detailRow}>
                      <Icon name="table-restaurant" size={16} color="#666" />
                      <Text style={styles.detailLabel}>Masa:</Text>
                      <Text style={styles.detailValue}>{item.table_title}</Text>
                    </View>
                  )}

                  {item.delivery_type && (
                    <View style={styles.detailRow}>
                      <Icon name="delivery-dining" size={16} color="#666" />
                      <Text style={styles.detailLabel}>Teslimat Türü:</Text>
                      <Text style={styles.detailValue}>
                        {item.delivery_type === 'dinein' ? 'Masa Servisi' :
                         item.delivery_type === 'takeaway' ? 'Paket Servis' :
                         item.delivery_type === 'delivery' ? 'Teslimat' : item.delivery_type}
                      </Text>
                    </View>
                  )}

                  {item.customer_name && (
                    <View style={styles.detailRow}>
                      <Icon name="person" size={16} color="#666" />
                      <Text style={styles.detailLabel}>Müşteri:</Text>
                      <Text style={styles.detailValue}>{item.customer_name}</Text>
                    </View>
                  )}

                  {item.customer_type && (
                    <View style={styles.detailRow}>
                      <Icon name="badge" size={16} color="#666" />
                      <Text style={styles.detailLabel}>Müşteri Tipi:</Text>
                      <Text style={styles.detailValue}>{item.customer_type}</Text>
                    </View>
                  )}

                  {item.item_title && (
                    <View style={styles.detailRow}>
                      <Icon name="restaurant-menu" size={16} color="#666" />
                      <Text style={styles.detailLabel}>Ürün:</Text>
                      <Text style={styles.detailValue}>
                        {item.item_title}
                        {item.variant_title && ` (${item.variant_title})`}
                      </Text>
                    </View>
                  )}

                  {item.price && (
                    <View style={styles.detailRow}>
                      <Icon name="attach-money" size={16} color="#666" />
                      <Text style={styles.detailLabel}>Birim Fiyat:</Text>
                      <Text style={styles.detailValue}>{formatCurrency(item.price)}</Text>
                    </View>
                  )}

                  {item.quantity && (
                    <View style={styles.detailRow}>
                      <Icon name="numbers" size={16} color="#666" />
                      <Text style={styles.detailLabel}>Miktar:</Text>
                      <Text style={styles.detailValue}>{item.quantity}</Text>
                    </View>
                  )}

                  {item.total_price && (
                    <View style={styles.detailRow}>
                      <Icon name="calculate" size={16} color="#666" />
                      <Text style={styles.detailLabel}>Toplam Fiyat:</Text>
                      <Text style={styles.detailValue}>{formatCurrency(item.total_price)}</Text>
                    </View>
                  )}
                </View>

                {item.description && (
                  <View style={styles.descriptionInfo}>
                    <Icon name="description" size={16} color="#666" />
                    <Text style={styles.descriptionText}>{item.description}</Text>
                  </View>
                )}

                {item.created_at && (
                  <View style={styles.timeInfo}>
                    <Icon name="access-time" size={16} color="#666" />
                    <Text style={styles.timeText}>İşlem Zamanı: {formatTurkishDateTime(item.created_at)}</Text>
                  </View>
                )}

                {item.user_name && (
                  <View style={styles.userInfo}>
                    <Icon name="person-outline" size={16} color="#666" />
                    <Text style={styles.userText}>İşlem Yapan: {item.user_name}</Text>
                  </View>
                )}

                {item.username && (
                  <View style={styles.userInfo}>
                    <Icon name="email" size={16} color="#666" />
                    <Text style={styles.userText}>Kullanıcı: {item.username}</Text>
                  </View>
                )}
              </View>
            ))}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="credit-card" size={60} color="#607D8B" />
            <Text style={styles.emptyText}>Ödenmez işlemi bulunmuyor</Text>
            <Text style={styles.emptySubText}>
              Ödenmez işlemleri burada görüntülenecektir
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingTop: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  summaryContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  scrollContainer: {
    flex: 1,
  },
  sectionContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  orderItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#607D8B',
  },
  creditInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#eceff1',
    padding: 8,
    borderRadius: 4,
    marginBottom: 8,
  },
  creditText: {
    fontSize: 12,
    color: '#607D8B',
    marginLeft: 4,
    fontWeight: '500',
  },
  descriptionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  descriptionText: {
    fontSize: 14,
    color: '#333',
    marginLeft: 4,
  },
  timeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  timeText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 10,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: '#ccc',
    marginTop: 8,
    textAlign: 'center',
  },
  orderDetailsContainer: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 4,
  },
  detailLabel: {
    fontSize: 13,
    color: '#666',
    marginLeft: 8,
    minWidth: 100,
  },
  detailValue: {
    fontSize: 13,
    color: '#333',
    fontWeight: '500',
    flex: 1,
    marginLeft: 8,
  },
});

export default CreditDetailScreen;
