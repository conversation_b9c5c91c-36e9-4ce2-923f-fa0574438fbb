import React, { useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Modal,
  SafeAreaView,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { MenuItem, MenuItemVariant, MenuItemAddon } from '../types';

interface ItemCustomizationModalProps {
  visible: boolean;
  item: MenuItem | null;
  onClose: () => void;
  onAddToCart: (
    item: MenuItem,
    selectedVariant?: MenuItemVariant,
    selectedAddons?: MenuItemAddon[]
  ) => void;
}

const ItemCustomizationModal: React.FC<ItemCustomizationModalProps> = ({
  visible,
  item,
  onClose,
  onAddToCart,
}) => {
  const [selectedVariant, setSelectedVariant] = useState<MenuItemVariant | undefined>(undefined);
  const [selectedAddons, setSelectedAddons] = useState<MenuItemAddon[]>([]);

  if (!item) return null;

  const hasVariants = item.variants && item.variants.length > 0;
  const hasAddons = item.addons && item.addons.length > 0;

  // Eğer varyasyon ve ekstra yoksa direkt sepete ekle
  if (!hasVariants && !hasAddons) {
    onAddToCart(item);
    onClose();
    return null;
  }

  const calculateTotalPrice = (): number => {
    let basePrice = parseFloat(item.price);
    
    if (selectedVariant) {
      basePrice = parseFloat(selectedVariant.price);
    }

    const addonsPrice = selectedAddons.reduce((sum, addon) => {
      return sum + parseFloat(addon.price);
    }, 0);

    return basePrice + addonsPrice;
  };

  const formatCurrency = (amount: number): string => {
    return `${amount.toFixed(2)} ₺`;
  };

  const handleVariantSelect = (variant: MenuItemVariant) => {
    setSelectedVariant(variant);
  };

  const handleAddonToggle = (addon: MenuItemAddon) => {
    const isSelected = selectedAddons.find(a => a.id === addon.id);
    if (isSelected) {
      setSelectedAddons(selectedAddons.filter(a => a.id !== addon.id));
    } else {
      setSelectedAddons([...selectedAddons, addon]);
    }
  };

  const handleAddToCart = () => {
    onAddToCart(item, selectedVariant, selectedAddons);
    setSelectedVariant(undefined);
    setSelectedAddons([]);
    onClose();
  };

  const isAddDisabled = hasVariants && !selectedVariant;

  return (
    <Modal
      visible={visible}
      animationType="slide"
      presentationStyle="pageSheet"
      onRequestClose={onClose}
    >
      <SafeAreaView style={styles.container}>
        {/* Header */}
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <Icon name="close" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Ürün Seçenekleri</Text>
          <View style={styles.headerSpacer} />
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          {/* Ürün Bilgisi */}
          <View style={styles.itemInfo}>
            <Text style={styles.itemTitle}>{item.title}</Text>
            <Text style={styles.itemDescription}>{item.description}</Text>
            <Text style={styles.itemCategory}>{item.category_title}</Text>
          </View>

          {/* Varyasyonlar */}
          {hasVariants && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>
                Varyasyon Seçin <Text style={styles.required}>*</Text>
              </Text>
              {item.variants!.map((variant) => (
                <TouchableOpacity
                  key={variant.id}
                  style={[
                    styles.optionItem,
                    selectedVariant?.id === variant.id && styles.selectedOption,
                  ]}
                  onPress={() => handleVariantSelect(variant)}
                >
                  <View style={styles.optionContent}>
                    <Text style={[
                      styles.optionTitle,
                      selectedVariant?.id === variant.id && styles.selectedOptionText,
                    ]}>
                      {variant.title}
                    </Text>
                    <Text style={[
                      styles.optionPrice,
                      selectedVariant?.id === variant.id && styles.selectedOptionText,
                    ]}>
                      {formatCurrency(parseFloat(variant.price))}
                    </Text>
                  </View>
                  {selectedVariant?.id === variant.id && (
                    <Icon name="check-circle" size={20} color="#4CAF50" />
                  )}
                </TouchableOpacity>
              ))}
            </View>
          )}

          {/* Ekstralar */}
          {hasAddons && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Ekstralar (İsteğe Bağlı)</Text>
              {item.addons!.map((addon) => {
                const isSelected = selectedAddons.find(a => a.id === addon.id);
                return (
                  <TouchableOpacity
                    key={addon.id}
                    style={[
                      styles.optionItem,
                      isSelected && styles.selectedOption,
                    ]}
                    onPress={() => handleAddonToggle(addon)}
                  >
                    <View style={styles.optionContent}>
                      <Text style={[
                        styles.optionTitle,
                        isSelected && styles.selectedOptionText,
                      ]}>
                        {addon.title}
                      </Text>
                      <Text style={[
                        styles.optionPrice,
                        isSelected && styles.selectedOptionText,
                      ]}>
                        +{formatCurrency(parseFloat(addon.price))}
                      </Text>
                    </View>
                    {isSelected && (
                      <Icon name="check-circle" size={20} color="#4CAF50" />
                    )}
                  </TouchableOpacity>
                );
              })}
            </View>
          )}
        </ScrollView>

        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.priceContainer}>
            <Text style={styles.totalLabel}>Toplam:</Text>
            <Text style={styles.totalPrice}>
              {formatCurrency(calculateTotalPrice())}
            </Text>
          </View>
          <TouchableOpacity
            style={[styles.addButton, isAddDisabled && styles.disabledButton]}
            onPress={handleAddToCart}
            disabled={isAddDisabled}
          >
            <Icon name="add-shopping-cart" size={20} color="#fff" />
            <Text style={styles.addButtonText}>Sepete Ekle</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  closeButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    padding: 16,
  },
  itemInfo: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 16,
  },
  itemTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  itemDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
  },
  itemCategory: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 12,
  },
  required: {
    color: '#F44336',
  },
  optionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    marginBottom: 8,
  },
  selectedOption: {
    borderColor: '#4CAF50',
    backgroundColor: '#f1f8e9',
  },
  optionContent: {
    flex: 1,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  optionTitle: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  optionPrice: {
    fontSize: 14,
    color: '#666',
    fontWeight: 'bold',
  },
  selectedOptionText: {
    color: '#4CAF50',
  },
  footer: {
    backgroundColor: '#fff',
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  priceContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  totalLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  totalPrice: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  addButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    borderRadius: 8,
    gap: 8,
  },
  disabledButton: {
    backgroundColor: '#ccc',
  },
  addButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
});

export default ItemCustomizationModal;
