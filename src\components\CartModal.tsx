import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import OrderSummary from './OrderSummary';
import Toast from './Toast';
import { CartItem } from '../types';
import { SafeAreaFrameContext, SafeAreaView } from 'react-native-safe-area-context';

interface CartModalProps {
  items: CartItem[];
  onClose: () => void;
  onCreateOrder: () => void;
  onIncrementItem: (item: CartItem) => void;
  onDecrementItem: (item: CartItem) => void;
  onRemoveItem: (item: CartItem) => void;
  toastMessage?: string;
  toastType?: 'success' | 'error' | 'warning' | 'info';
}

const CartModal: React.FC<CartModalProps> = ({ 
  items, 
  onClose, 
  onCreateOrder, 
  onIncrementItem, 
  onDecrementItem, 
  onRemoveItem,
  toastMessage,
  toastType = 'success'
}) => {
  const isOrderDisabled = items.length === 0;

  return (
    <SafeAreaView style={styles.container}>
      {toastMessage && (
        <Toast 
          message={toastMessage} 
          type={toastType}
          duration={3000}
        />
      )}
      
      {/* Üst Bar */}
      <View style={styles.header}>
        <TouchableOpacity 
          onPress={onClose}
          style={styles.backButton}
          activeOpacity={0.7}
        >
          <Icon name="arrow-back" size={24} color="#fff" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Sipariş Özeti</Text>
        <View style={styles.headerRight}>
          <Text style={styles.itemCount}>
            {items.length} ürün
          </Text>
        </View>
      </View>

      {/* Sipariş Özeti */}
      <ScrollView 
        style={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <OrderSummary 
          items={items} 
          onIncrementItem={onIncrementItem} 
          onDecrementItem={onDecrementItem} 
          onRemoveItem={onRemoveItem}
        />
      </ScrollView>

      {/* Alt Butonlar */}
      <View style={styles.actionContainer}>
        <TouchableOpacity
          style={[
            styles.actionButton,
            isOrderDisabled && styles.actionButtonDisabled
          ]}
          onPress={onCreateOrder}
          disabled={isOrderDisabled}
          activeOpacity={0.8}
        >
          <Icon 
            name="send" 
            size={20} 
            color="#fff" 
            style={styles.buttonIcon}
          />
          <Text style={[
            styles.actionButtonText,
            isOrderDisabled && styles.actionButtonTextDisabled
          ]}>
            Siparişi Gönder
          </Text>
        </TouchableOpacity>
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    position: 'absolute',
    backgroundColor: '#F5F5F5',
    height: '100%',
    width: '100%',
    zIndex: 100,
  },
  header: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    color: '#fff',
    fontWeight: 'bold',
    flex: 1,
    textAlign: 'center',
  },
  headerRight: {
    alignItems: 'flex-end',
  },
  itemCount: {
    fontSize: 14,
    color: '#fff',
    opacity: 0.9,
  },
  content: {
    flex: 1,
    padding: 15,
  },
  actionContainer: {
    padding: 15,
    backgroundColor: '#fff',
    borderTopWidth: 1,
    borderTopColor: '#E0E0E0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  actionButton: {
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
    flexDirection: 'row',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 3,
  },
  actionButtonDisabled: {
    backgroundColor: '#CCCCCC',
  },
  buttonIcon: {
    marginRight: 8,
  },
  actionButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
  actionButtonTextDisabled: {
    color: '#999',
  },
});

export default CartModal;
