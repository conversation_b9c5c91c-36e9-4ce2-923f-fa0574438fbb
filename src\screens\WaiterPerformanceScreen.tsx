import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  TextInput,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { getReports } from '../config/api';
import { formatCurrency, formatTurkishDateTime } from '../utils/dateUtils';

type NavigationProp = StackNavigationProp<RootStackParamList, 'WaiterPerformance'>;
type RoutePropType = RouteProp<RootStackParamList, 'WaiterPerformance'>;

interface WaiterSalesReport {
  waiter_username: string;
  waiter_name: string;
  total_orders: number;
  total_sales: string;
  total_items_sold: number;
  item_details: ItemDetail[];
}

interface ItemDetail {
  item_title: string;
  variant_title: string | null;
  price: string;
  total_quantity: string;
  total_amount: string;
  order_count: number;
}

const WaiterPerformanceScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RoutePropType>();
  const { reportType, startDate, endDate } = route.params;

  const [waiterData, setWaiterData] = useState<WaiterSalesReport[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [selectedWaiter, setSelectedWaiter] = useState<string>('all');
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    fetchWaiterData();
  }, []);

  const fetchWaiterData = async (): Promise<void> => {
    try {
      setIsLoading(true);

      let response;
      if (reportType === 'custom' && startDate && endDate) {
        response = await getReports(reportType, startDate, endDate);
      } else {
        response = await getReports(reportType);
      }


      if (response && (response as any).waiterSalesReport) {
        setWaiterData((response as any).waiterSalesReport);
      } else {
      }
    } catch (error) {
      console.error('Garson performans verileri yüklenirken hata:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async (): Promise<void> => {
    setRefreshing(true);
    await fetchWaiterData();
    setRefreshing(false);
  };

  const getFilteredWaiters = (): WaiterSalesReport[] => {
    return waiterData.filter(waiter => {
      if (selectedWaiter !== 'all' && waiter.waiter_name !== selectedWaiter) {
        return false;
      }
      return true;
    });
  };

  const getFilteredItems = (): ItemDetail[] => {
    const allItems: ItemDetail[] = [];
    
    getFilteredWaiters().forEach(waiter => {
      waiter.item_details.forEach(item => {
        const existingItem = allItems.find(
          existing => existing.item_title === item.item_title && 
                     existing.variant_title === item.variant_title
        );
        
        if (existingItem) {
          existingItem.total_quantity = (parseInt(existingItem.total_quantity) + parseInt(item.total_quantity)).toString();
          existingItem.total_amount = (parseFloat(existingItem.total_amount) + parseFloat(item.total_amount)).toString();
          existingItem.order_count += item.order_count;
        } else {
          allItems.push({ ...item });
        }
      });
    });

    if (searchQuery) {
      return allItems.filter(item => 
        item.item_title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.variant_title && item.variant_title.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    return allItems.sort((a, b) => parseFloat(b.total_amount) - parseFloat(a.total_amount));
  };

  const getWaitersByItem = (itemTitle: string, variantTitle: string | null): WaiterSalesReport[] => {
    return waiterData.filter(waiter => 
      waiter.item_details.some(item => 
        item.item_title === itemTitle && item.variant_title === variantTitle
      )
    ).map(waiter => ({
      ...waiter,
      item_details: waiter.item_details.filter(item => 
        item.item_title === itemTitle && item.variant_title === variantTitle
      )
    }));
  };

  const renderWaiterCard = ({ item }: { item: WaiterSalesReport }) => (
    <View style={styles.waiterCard}>
      <View style={styles.waiterHeader}>
        <Text style={styles.waiterName}>{item.waiter_name}</Text>
        <Text style={styles.waiterSales}>{formatCurrency(item.total_sales)}</Text>
      </View>
      
      <View style={styles.waiterStats}>
        <View style={styles.statItem}>
          <Icon name="receipt" size={16} color="#666" />
          <Text style={styles.statText}>{item.total_orders} Sipariş</Text>
        </View>
        <View style={styles.statItem}>
          <Icon name="shopping-cart" size={16} color="#666" />
          <Text style={styles.statText}>{item.total_items_sold} Ürün</Text>
        </View>
      </View>

      <Text style={styles.itemsTitle}>Sattığı Ürünler:</Text>
      {item.item_details.map((detail, index) => (
        <View key={index} style={styles.itemRow}>
          <Text style={styles.itemName}>
            {detail.item_title}
            {detail.variant_title && ` (${detail.variant_title})`}
          </Text>
          <Text style={styles.itemQuantity}>x{detail.total_quantity}</Text>
          <Text style={styles.itemAmount}>{formatCurrency(detail.total_amount)}</Text>
        </View>
      ))}
    </View>
  );

  const renderItemCard = ({ item }: { item: ItemDetail }) => {
    const waitersForItem = getWaitersByItem(item.item_title, item.variant_title);
    
    return (
      <View style={styles.itemCard}>
        <View style={styles.itemHeader}>
          <Text style={styles.itemTitle}>
            {item.item_title}
            {item.variant_title && ` (${item.variant_title})`}
          </Text>
          <Text style={styles.itemTotalAmount}>{formatCurrency(item.total_amount)}</Text>
        </View>
        
        <View style={styles.itemStats}>
          <Text style={styles.itemStat}>Toplam: {item.total_quantity} adet</Text>
          <Text style={styles.itemStat}>Sipariş: {item.order_count} kez</Text>
        </View>

        <Text style={styles.waitersTitle}>Satan Garsonlar:</Text>
        {waitersForItem.map((waiter, index) => (
          <View key={index} style={styles.waiterItemRow}>
            <Text style={styles.waiterItemName}>{waiter.waiter_name}</Text>
            <Text style={styles.waiterItemQuantity}>
              x{waiter.item_details[0]?.total_quantity || 0}
            </Text>
            <Text style={styles.waiterItemAmount}>
              {formatCurrency(waiter.item_details[0]?.total_amount || '0')}
            </Text>
          </View>
        ))}
      </View>
    );
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Garson Performansı</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Veriler yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Garson Performansı</Text>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* Filtreler */}
        <View style={styles.filterContainer}>
          <Text style={styles.filterLabel}>Garson Filtresi:</Text>
          <ScrollView horizontal showsHorizontalScrollIndicator={false}>
            <TouchableOpacity
              style={[styles.filterChip, selectedWaiter === 'all' && styles.activeFilterChip]}
              onPress={() => setSelectedWaiter('all')}
            >
              <Text style={[styles.filterChipText, selectedWaiter === 'all' && styles.activeFilterChipText]}>
                Tümü
              </Text>
            </TouchableOpacity>
            {waiterData.map((waiter) => (
              <TouchableOpacity
                key={waiter.waiter_username}
                style={[styles.filterChip, selectedWaiter === waiter.waiter_name && styles.activeFilterChip]}
                onPress={() => setSelectedWaiter(waiter.waiter_name)}
              >
                <Text style={[styles.filterChipText, selectedWaiter === waiter.waiter_name && styles.activeFilterChipText]}>
                  {waiter.waiter_name}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>

        <TextInput
          style={styles.searchInput}
          placeholder="Ürün ara..."
          value={searchQuery}
          onChangeText={setSearchQuery}
        />

        {waiterData.length > 0 ? (
          <>
            {/* Garson Performansı */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Garson Performansı</Text>
              <FlatList
                data={getFilteredWaiters()}
                renderItem={renderWaiterCard}
                keyExtractor={item => item.waiter_username}
                scrollEnabled={false}
              />
            </View>

            {/* Ürün Bazlı Analiz */}
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Ürün Bazlı Satışlar</Text>
              <FlatList
                data={getFilteredItems()}
                renderItem={renderItemCard}
                keyExtractor={(item, index) => `${item.item_title}-${item.variant_title}-${index}`}
                scrollEnabled={false}
              />
            </View>
          </>
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="people-outline" size={60} color="#ccc" />
            <Text style={styles.emptyText}>Garson performans verisi bulunamadı</Text>
            <Text style={styles.emptySubText}>
              Seçilen tarih aralığında garson satış verisi bulunmuyor.
            </Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  filterContainer: {
    backgroundColor: '#fff',
    padding: 15,
    marginBottom: 10,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  filterChip: {
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
  },
  activeFilterChip: {
    backgroundColor: '#2196F3',
  },
  filterChipText: {
    fontSize: 14,
    color: '#666',
  },
  activeFilterChipText: {
    color: '#fff',
  },
  searchInput: {
    backgroundColor: '#fff',
    margin: 10,
    paddingHorizontal: 15,
    paddingVertical: 12,
    borderRadius: 8,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  section: {
    backgroundColor: '#fff',
    margin: 10,
    borderRadius: 8,
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  waiterCard: {
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  waiterHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  waiterName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  waiterSales: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  waiterStats: {
    flexDirection: 'row',
    gap: 20,
    marginBottom: 10,
  },
  statItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  statText: {
    fontSize: 14,
    color: '#666',
  },
  itemsTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  itemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  itemName: {
    flex: 1,
    fontSize: 13,
    color: '#666',
  },
  itemQuantity: {
    fontSize: 13,
    color: '#666',
    marginHorizontal: 10,
  },
  itemAmount: {
    fontSize: 13,
    fontWeight: '600',
    color: '#4CAF50',
  },
  itemCard: {
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 8,
    marginBottom: 10,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  itemTotalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  itemStats: {
    flexDirection: 'row',
    gap: 20,
    marginBottom: 10,
  },
  itemStat: {
    fontSize: 14,
    color: '#666',
  },
  waitersTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  waiterItemRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 4,
  },
  waiterItemName: {
    flex: 1,
    fontSize: 13,
    color: '#666',
  },
  waiterItemQuantity: {
    fontSize: 13,
    color: '#666',
    marginHorizontal: 10,
  },
  waiterItemAmount: {
    fontSize: 13,
    fontWeight: '600',
    color: '#4CAF50',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    margin: 20,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#666',
    marginTop: 16,
    textAlign: 'center',
  },
  emptySubText: {
    fontSize: 14,
    color: '#999',
    marginTop: 8,
    textAlign: 'center',
    lineHeight: 20,
  },
});

export default WaiterPerformanceScreen;
