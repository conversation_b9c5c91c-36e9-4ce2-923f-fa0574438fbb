import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  FlatList,
  ScrollView
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { formatTurkishTime, formatCurrency } from '../utils/dateUtils';

interface OrderItem {
  id: number;
  order_id: number;
  item_id: number;
  item_title: string;
  variant_id: number | null;
  variant_title: string | null;
  price: string;
  quantity: number;
  status: string;
  date: string;
  addons: any[];
  notes: string | null;
}

interface Order {
  id: number;
  date: string;
  delivery_type: string;
  customer_type: string;
  table_id: number;
  table_title: string;
  status: string;
  payment_status: string;
  token_no: number;
  username: string;
  user_name: string;
  items: OrderItem[];
  partialPayments: any[];
}

interface Table {
  id: number;
  encrypted_id: string;
  table_title: string;
  table_status: 'empty' | 'busy' | 'locked';
  floor: number;
  seating_capacity: number;
  orders: Order[];
  order_ids: number[];
}

interface TableItemsModalProps {
  visible: boolean;
  table: Table | null;
  onClose: () => void;
}

const TableItemsModal: React.FC<TableItemsModalProps> = ({
  visible,
  table,
  onClose
}) => {
  if (!table) return null;



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'created': return '#FF9800';
      case 'preparing': return '#2196F3';
      case 'ready': return '#4CAF50';
      case 'served': return '#9E9E9E';
      default: return '#666';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'created': return 'Oluşturuldu';
      case 'preparing': return 'Hazırlanıyor';
      case 'ready': return 'Hazır';
      case 'served': return 'Servis Edildi';
      default: return status;
    }
  };

  const renderOrderItem = ({ item }: { item: OrderItem }) => (
    <View style={styles.itemContainer}>
      <View style={styles.itemHeader}>
        <Text style={styles.itemTitle}>{item.item_title}</Text>
        <View style={styles.itemPriceContainer}>
          <Text style={styles.itemQuantity}>x{item.quantity}</Text>
          <Text style={styles.itemPrice}>
            {formatCurrency(parseFloat(item.price) * item.quantity)}
          </Text>
        </View>
      </View>
      
      {item.variant_title && (
        <Text style={styles.itemVariant}>Varyant: {item.variant_title}</Text>
      )}
      
      {item.notes && (
        <Text style={styles.itemNotes}>Not: {item.notes}</Text>
      )}
      
      <View style={styles.itemFooter}>
        <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
        </View>
        <Text style={styles.itemTime}>{formatTurkishTime(item.date)}</Text>
      </View>
    </View>
  );

  const renderOrder = ({ item: order }: { item: Order }) => (
    <View style={styles.orderContainer}>
      <View style={styles.orderHeader}>
        <Text style={styles.orderTitle}>Sipariş #{order.id}</Text>
        <Text style={styles.orderTime}>{formatTurkishTime(order.date)}</Text>
      </View>
      
      <FlatList
        data={order.items}
        renderItem={renderOrderItem}
        keyExtractor={item => item.id.toString()}
        scrollEnabled={false}
      />
      
      <View style={styles.orderTotal}>
        <Text style={styles.orderTotalText}>
          Sipariş Toplamı: {formatCurrency(
            order.items.reduce((total, item) => 
              total + (parseFloat(item.price) * item.quantity), 0
            )
          )}
        </Text>
      </View>
    </View>
  );

  const totalAmount = table.orders.reduce((total, order) => 
    total + order.items.reduce((orderTotal, item) => 
      orderTotal + (parseFloat(item.price) * item.quantity), 0
    ), 0
  );

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="slide"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>
              {table.table_title} - Ürünler
            </Text>
            <TouchableOpacity
              onPress={onClose}
              style={styles.modalCloseButton}
            >
              <Icon name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <ScrollView style={styles.modalContent}>
            <FlatList
              data={table.orders}
              renderItem={renderOrder}
              keyExtractor={order => order.id.toString()}
              scrollEnabled={false}
            />
          </ScrollView>

          <View style={styles.modalFooter}>
            <Text style={styles.grandTotal}>
              Genel Toplam: {formatCurrency(totalAmount)}
            </Text>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    margin: 20,
    maxHeight: '80%',
    width: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalContent: {
    maxHeight: 400,
  },
  orderContainer: {
    margin: 15,
    padding: 15,
    backgroundColor: '#f9f9f9',
    borderRadius: 10,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  orderTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  orderTime: {
    fontSize: 14,
    color: '#666',
  },
  itemContainer: {
    backgroundColor: '#fff',
    padding: 12,
    marginVertical: 4,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#4CAF50',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  itemPriceContainer: {
    alignItems: 'flex-end',
  },
  itemQuantity: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  itemVariant: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  itemNotes: {
    fontSize: 14,
    color: '#FF9800',
    marginBottom: 8,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  itemTime: {
    fontSize: 12,
    color: '#999',
  },
  orderTotal: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  orderTotalText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'right',
  },
  modalFooter: {
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    backgroundColor: '#f9f9f9',
  },
  grandTotal: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
    textAlign: 'center',
  },
});

export default TableItemsModal;
