// Common Types
export interface BaseEntity {
  id: number;
  created_at?: string;
  updated_at?: string;
}

// User Types
export interface User extends BaseEntity {
  username: string;
  email: string;
  role: 'admin' | 'cashier' | 'waiter';
  is_active: boolean;
}

// Menu Item Types
export interface MenuItemVariant {
  id: number;
  item_id: number;
  title: string;
  price: string;
}

export interface MenuItemAddon {
  id: number;
  item_id: number;
  title: string;
  price: string;
}

export interface MenuItem extends BaseEntity {
  title: string;
  price: string;
  net_price: string;
  category_id: number;
  category_title: string;
  description?: string;
  is_enabled: number;
  variants?: MenuItemVariant[];
  addons?: MenuItemAddon[];
  recipeItems?: any[];
}

// Cart Item Types
export interface CartItem extends MenuItem {
  quantity: number;
  selectedVariant?: MenuItemVariant;
  selectedAddons?: MenuItemAddon[];
  totalPrice?: number;
  uniqueKey?: string;
}

// Table Types
export interface Table extends BaseEntity {
  table_number: number;
  floor: number;
  capacity: number;
  status: 'available' | 'occupied' | 'reserved';
}

// Order Types
export interface Order extends BaseEntity {
  table_id: number;
  user_id: number;
  total_amount: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  items: OrderItem[];
}

export interface OrderItem {
  menu_item_id: number;
  quantity: number;
  price: number;
  title: string;
}

// Inventory Types
export interface InventoryItem extends BaseEntity {
  name: string;
  category: string;
  quantity: number;
  unit: string;
  minLevel: number;
  price: number;
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

// Navigation Types
export type RootStackParamList = {
  Login: undefined;
  Dashboard: undefined;
  Main: undefined;
  Pos: { tableId?: number };
  Reports: undefined;
  Inventory: undefined;
  InventoryDashboard: undefined;
  CashRegisterDetail: { sessionId: number };
  Settings: undefined;
  OpenOrdersDetail: {
    openOrders: {
      dinein: any[];
      takeaway: any[];
      delivery: any[];
      unknown: any[];
    };
    reportType: string;
    startDate?: string;
    endDate?: string;
  };
  CancelledOrdersDetail: {
    reportType: string;
    startDate?: string;
    endDate?: string;
  };
  WasteDetail: {
    reportType: string;
    startDate?: string;
    endDate?: string;
  };
  ComplimentaryDetail: {
    reportType: string;
    startDate?: string;
    endDate?: string;
  };
  DiscountDetail: {
    reportType: string;
    startDate?: string;
    endDate?: string;
  };
  CreditDetail: {
    reportType: string;
    startDate?: string;
    endDate?: string;
  };
  OrderDetails: {
    reportType: string;
    startDate?: string;
    endDate?: string;
  };
  SingleOrderDetail: {
    order: any;
    orderItems: any[];
    payments: any[];
  };
  WaiterPerformance: {
    reportType: string;
    startDate?: string;
    endDate?: string;
  };
};

// Component Props Types
export interface ModalProps {
  isVisible: boolean;
  onClose: () => void;
}

export interface ToastProps {
  message: string;
  type: 'success' | 'error' | 'warning' | 'info';
  visible: boolean;
  onHide: () => void;
}

// Form Types
export interface LoginFormData {
  username: string;
  password: string;
}

// Report Types
export interface SalesReport {
  date: string;
  total_sales: number;
  order_count: number;
  top_items: Array<{
    item_name: string;
    quantity_sold: number;
    revenue: number;
  }>;
  cancelledOrdersAndItems?: {
    total: string | number;
    details?: any[];
  };
  wasteTotal?: number | {
    total: string | number;
    details?: any[];
  };
  complimentaryTotal?: number | {
    total: string | number;
    details?: any[];
  };
  getTotalDiscounts?: number | {
    total: string | number;
    details?: any[];
  };
}

export interface ExtendedSalesReport extends SalesReport {
  ordersCount?: number;
  openOrders?: {
    dinein: any[];
    takeaway: any[];
    delivery: any[];
    unknown: any[];
  };
  openOrdersTotal?: number;
  cancelledOrdersAndItems?: {
    total: string;
    details: any[];
  };
  newCustomers?: number;
  repeatedCustomers?: number;
  currency?: string;
  averageOrderValue?: number | null;
  totalCustomers?: number;
  netRevenue?: string;
  netRevenueByFloor?: Array<{
    floor_id: number;
    floor_title: string;
    floor_description: string | null;
    net_revenue: string;
    order_count: number;
    customer_count: number;
    avg_order_value: string;
  }>;
  taxTotal?: number | null;
  revenueTotal?: string;
  topSellingItems?: Array<{
    id: number;
    title: string;
    price: string;
    net_price: string;
    orders_count: string;
    [key: string]: any;
  }>;
  creditTotal?: {
    total: string;
    details: any[];
  };
  getTotalDiscounts?: {
    total: string;
    details: any[];
  };
  wasteTotal?: {
    total: string;
    details: any[];
  };
  complimentaryTotal?: {
    total: string;
    details: any[];
  };
  totalPaymentsByPaymentTypes?: Array<{
    title: string;
    total: number;
  }>;
  waiterSalesReport?: Array<{
    waiter_username: string;
    waiter_name: string;
    total_orders: number;
    total_sales: string;
    total_items_sold: number;
    item_details: Array<{
      item_title: string;
      variant_title: string | null;
      price: string;
      total_quantity: string;
      total_amount: string;
      order_count: number;
    }>;
  }>;
}

export interface CashRegisterData {
  opening_balance: number;
  closing_balance: number;
  total_sales: number;
  cash_transactions: number;
  card_transactions: number;
  date: string;
}
