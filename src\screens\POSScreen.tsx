import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  FlatList,
  StyleSheet,
  TextInput,
  SafeAreaView,
  ListRenderItem,
  Modal
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { getPOSData, createOrder } from '../config/api';
import MenuItem from '../components/MenuItem';
import Icon from 'react-native-vector-icons/MaterialIcons';
import CartModal from '../components/CartModal';
import Toast from '../components/Toast';
import LoadingScreen from './LoadingScreen';
import TableModal from '../components/TableModal';
import TableItemsModal from '../components/TableItemsModal';
import ItemCustomizationModal from '../components/ItemCustomizationModal';
import { formatTurkishTime, formatCurrency } from '../utils/dateUtils';
import { RootStackParamList, CartItem, MenuItem as MenuItemType } from '../types';
import { initSocket, disconnectSocket, getSocket, isSocketConnected } from '../helpers/socket';
import AsyncStorage from '@react-native-async-storage/async-storage';
type POSScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Pos'>;

interface POSScreenProps {
  navigation: POSScreenNavigationProp;
}

interface Category {
  id: string;
  title: string;
}

interface Floor {
  floor_id: number;
  floor_name: string;
  tables: Table[];
}

interface Order {
  id: number;
  date: string;
  delivery_type: string;
  customer_type: string;
  table_id: number;
  table_title: string;
  status: string;
  payment_status: string;
  token_no: number;
  username: string;
  user_name: string;
  items: any[];
  partialPayments: any[];
}

interface Table {
  id: number;
  encrypted_id: string;
  table_title: string;
  table_status: 'empty' | 'busy' | 'locked';
  floor: number;
  seating_capacity: number;
  orders: Order[];
  order_ids: number[];
}

interface POSData {
  categories: Category[];
  menuItems: MenuItemType[];
  floors: Floor[];
}

const POSScreen: React.FC<POSScreenProps> = ({ navigation }) => {
  const [posData, setPosData] = useState<POSData | null>(null);
  const [orderItems, setOrderItems] = useState<CartItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('Tümü');
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [customerType, setCustomerType] = useState<string>('WALKIN');
  const [customer, setCustomer] = useState<any>(null);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [isCartVisible, setIsCartVisible] = useState<boolean>(false);
  const [toastMessage, setToastMessage] = useState<string>('');
  const [selectedFloor, setSelectedFloor] = useState<Floor | null>(null);
  const [isTableModalVisible, setIsTableModalVisible] = useState<boolean>(false);
  const [selectedOccupiedTable, setSelectedOccupiedTable] = useState<Table | null>(null);
  const [tableFilter, setTableFilter] = useState<'all' | 'empty' | 'busy'>('all');
  const [isTableItemsModalVisible, setIsTableItemsModalVisible] = useState<boolean>(false);
  const [qrOrdersCount, setQrOrdersCount] = useState<number>(0);
  const [isCustomizationModalVisible, setIsCustomizationModalVisible] = useState<boolean>(false );
  const [selectedItemForCustomization, setSelectedItemForCustomization] = useState<MenuItemType | null>(null);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    fetchPOSData();
    loadUserAndInitSocket();

    // Cleanup function
    return () => {
      disconnectSocket();
    };
  }, []);

  const loadUserAndInitSocket = async () => {
    try {
      
      const userData = await AsyncStorage.getItem('user');
      

      if (userData) {
        const parsedUser = JSON.parse(userData);
        console.log('👤 Parsed user:', parsedUser);
        setUser(parsedUser);
        initSocketConnection(parsedUser);
      } else {
        console.log('❌ No user data found in AsyncStorage');
      }
    } catch (error) {
      console.error('🔥 Error loading user data:', error);
    }
  };

  const initSocketConnection = (userData: any) => {
    

    if (isSocketConnected()) {
     
      const socket = getSocket();
      if (socket) {
        
        socket.emit("authenticate", userData.tenant_id);
        setupSocketListeners(socket);
      }
    } else {
      console.log('🔄 Socket not connected, creating new connection');
      initSocket();
      const socket = getSocket();
      if (socket) {
       
        socket.emit("authenticate", userData.tenant_id);
        setupSocketListeners(socket);
      } else {
        
      }
    }
  };

  const setupSocketListeners = (socket: any) => {
    // QR sipariş dinleme
    socket.on('new_qrorder', async (payload: any) => {
      try {
        // QR siparişleri sayısını güncelle (gerekirse API çağrısı yapabilirsiniz)
        setQrOrdersCount(prev => prev + 1);
        setToastMessage('Yeni QR sipariş alındı!');
      } catch (error) {
        console.error('Error handling new QR order:', error);
      }
    });

    // Yeni sipariş dinleme
    socket.on('new_order', async (payload: any) => {
      try {
        await refreshOrders();
        
      } catch (error) {
        console.error('Error handling new order:', error);
      }
    });

    // Sipariş güncelleme dinleme
    socket.on('order_update', async (payload: any) => {
      try {
        await refreshOrders();
        
      } catch (error) {
        console.error('Error handling order update:', error);
      }
    });
  };

  const refreshOrders = async () => {
    try {
      await fetchPOSDataSocket(); // POS verilerini yenile (masalar, siparişler vs.)
    } catch (error) {
      console.error('Error refreshing orders:', error);
    }
  };

  const sendNewOrderEvent = (tokenNo: number, orderId: number) => {
    const socket = getSocket();
    if (isSocketConnected() && socket && user) {
      socket.emit('new_order_backend', { tokenNo, orderId }, user.tenant_id);
      socket.emit('new_order');
      refreshOrders();
    } else {
      // Socket bağlantısı yoksa yeniden bağlan
      initSocket();
      const newSocket = getSocket();
      if (newSocket && user) {
        newSocket.emit('new_order_backend', { tokenNo, orderId }, user.tenant_id);
        newSocket.emit('new_order');
        refreshOrders();
      }
    }
  };

  const fetchPOSData = async (): Promise<void> => {
    setIsLoading(true);
    try {
      const data = await getPOSData();
      setPosData(data);

      if (data.floors && data.floors.length > 0) {
        setSelectedFloor(data.floors[0]);
      }

    } catch (error) {
      setToastMessage('POS verileri alınamadı. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

    const fetchPOSDataSocket = async (): Promise<void> => {
    
    try {
      const data = await getPOSData();
      setPosData(data);

      if (data.floors && data.floors.length > 0) {
        setSelectedFloor(data.floors[0]);
      }

    } catch (error) {
      setToastMessage('POS verileri alınamadı. Lütfen tekrar deneyin.');
    } finally {
      
    }
  };


  const handleAddItem = (item: MenuItemType): void => {
    const hasVariants = item.variants && item.variants.length > 0;
    const hasAddons = item.addons && item.addons.length > 0;

    // Eğer varyasyon veya ekstra varsa modal aç
    if (hasVariants || hasAddons) {
      setSelectedItemForCustomization(item);
      setIsCustomizationModalVisible(true);
      return;
    }

    // Varyasyon/ekstra yoksa direkt sepete ekle
    addItemToCart(item);
  };

  const addItemToCart = (
    item: MenuItemType,
    selectedVariant?: MenuItemVariant,
    selectedAddons?: MenuItemAddon[]
  ): void => {
    // Benzersiz ID oluştur (item + variant + addons kombinasyonu)
    const uniqueKey = `${item.id}_${selectedVariant?.id || 'no-variant'}_${selectedAddons?.map(a => a.id).join('-') || 'no-addons'}`;

    // Toplam fiyat hesapla
    let totalPrice = parseFloat(selectedVariant?.price || item.price);
    if (selectedAddons) {
      totalPrice += selectedAddons.reduce((sum, addon) => sum + parseFloat(addon.price), 0);
    }

    const cartItem: CartItem = {
      ...item,
      quantity: 1,
      selectedVariant: selectedVariant || undefined,
      selectedAddons: selectedAddons || undefined,
      totalPrice,
      uniqueKey, // Benzersiz anahtar ekle
    };

    // Aynı konfigürasyonla ürün var mı kontrol et
    const existingItem = orderItems.find((orderItem) => (orderItem as any).uniqueKey === uniqueKey);

    if (existingItem) {
      setOrderItems(
        orderItems.map((orderItem) =>
          (orderItem as any).uniqueKey === uniqueKey
            ? { ...orderItem, quantity: orderItem.quantity + 1 }
            : orderItem
        )
      );
      setToastMessage(`${item.title} sepette ${existingItem.quantity + 1} adet oldu.`);
    } else {
      setOrderItems([...orderItems, cartItem]);
      setToastMessage(`${item.title} sepetinize eklendi.`);
    }
  };

  const handleFloorSelect = (floor: Floor): void => {
    setSelectedFloor(floor);
    setSelectedTable(null);
  };

  const renderFloorButtons = () => {
    if (!posData?.floors || posData.floors.length === 0) {
      return <Text style={styles.noFloorsText}>Kat bilgisi bulunamadı</Text>;
    }

    const renderFloorItem: ListRenderItem<Floor> = ({ item }) => (
      <TouchableOpacity
        style={[
          styles.floorButton,
          selectedFloor?.floor_id === item.floor_id && styles.selectedFloor
        ]}
        onPress={() => handleFloorSelect(item)}
        activeOpacity={0.7}
      >
        <Text style={[
          styles.floorButtonText,
          selectedFloor?.floor_id === item.floor_id && styles.selectedFloorText
        ]}>
          {item.floor_name}
        </Text>
      </TouchableOpacity>
    );

    return (
      <FlatList
        horizontal
        data={posData.floors}
        renderItem={renderFloorItem}
        keyExtractor={item => item.floor_id.toString()}
        showsHorizontalScrollIndicator={false}
        style={styles.floorsList}
      />
    );
  };

  const renderTableFilter = () => {
    if (!selectedFloor) return null;

    const filterOptions = [
      { key: 'all', label: 'Tümü', icon: 'view-list' },
      { key: 'empty', label: 'Boş', icon: 'check-circle' },
      { key: 'busy', label: 'Dolu', icon: 'schedule' }
    ];

    return (
      <View style={styles.tableFilterContainer}>
        {filterOptions.map((option) => (
          <TouchableOpacity
            key={option.key}
            style={[
              styles.filterButton,
              tableFilter === option.key && styles.selectedFilter
            ]}
            onPress={() => setTableFilter(option.key as 'all' | 'empty' | 'busy')}
            activeOpacity={0.7}
          >
            <Icon
              name={option.icon}
              size={16}
              color={tableFilter === option.key ? '#fff' : '#666'}
            />
            <Text style={[
              styles.filterButtonText,
              tableFilter === option.key && styles.selectedFilterText
            ]}>
              {option.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const getTableStyle = (table: Table) => {
    if (table.table_status === 'locked') {
      return styles.lockedTable; // Gri
    }
    if (table.table_status === 'empty') {
      return styles.availableTable; // Yeşil
    }
    if (table.table_status === 'busy') {
      // 10 dakika kontrolü
      if (isTableOverdue(table)) {
        return styles.overdueTable; // Kırmızı (10 dakika geçmiş)
      }
      return styles.occupiedTable; // Sarı (dolu)
    }
    return styles.availableTable;
  };

  const isTableOverdue = (table: Table): boolean => {
    if (table.orders.length === 0) return false;
    const latestOrder = table.orders[table.orders.length - 1];
    if (!latestOrder) return false;
    const orderTime = new Date(latestOrder.date);
    const now = new Date();
    const diffMinutes = (now.getTime() - orderTime.getTime()) / (1000 * 60);
    return diffMinutes > 10;
  };

  const getTableTotalAmount = (table: Table): number => {
    return table.orders.reduce((total, order) => {
      return total + order.items.reduce((orderTotal, item) => {
        return orderTotal + (parseFloat(item.price) * item.quantity);
      }, 0);
    }, 0);
  };

  const getLatestOrder = (table: Table): Order | null => {
    if (table.orders.length === 0) return null;
    return table.orders[table.orders.length - 1] || null;
  };



  const renderTable: ListRenderItem<Table> = ({ item }) => (
    <TouchableOpacity
      style={[styles.tableButton, getTableStyle(item)]}
      onPress={() => {
        if (item.table_status === 'busy') {
          // Dolu masaya tıklanınca modal aç
          setSelectedOccupiedTable(item);
          setIsTableModalVisible(true);
        } else if (item.table_status === 'empty') {
          // Boş masaya tıklanınca masa seç
          setSelectedTable(item);
        }
      }}
      disabled={item.table_status === 'locked'}
      activeOpacity={0.7}
    >
      <Text style={styles.tableTitle}>{item.table_title}</Text>

      {item.table_status === 'busy' && (
        <>
          {(() => {
            const latestOrder = getLatestOrder(item);
            if (!latestOrder) return null;

            return (
              <>
                <View style={styles.tableInfoRow}>
                  <View style={styles.tableTimeContainer}>
                    <Icon name="access-time" size={12} color="#fff" />
                    <Text style={styles.tableOrderTime}>
                      {formatTurkishTime(latestOrder.date)}
                    </Text>
                  </View>

                  <View style={styles.tablePriceContainer}>
                    <Text style={styles.tableTotalAmount}>
                      {formatCurrency(getTableTotalAmount(item))}
                    </Text>
                  </View>
                </View>

                <View style={styles.tableWaiterRow}>
                  <View style={styles.tableWaiterContainer}>
                    
                    <Text style={styles.tableWaiterName}>
                      {latestOrder.user_name}
                    </Text>
                  </View>
                </View>
              </>
            );
          })()}
        </>
      )}

      {item.table_status === 'empty' && (
        <Text style={styles.tableStatus}>Müsait</Text>
      )}
      {item.table_status === 'locked' && (
        <Text style={styles.tableStatus}>Kilitli</Text>
      )}
    </TouchableOpacity>
  );

  const handleCreateOrder = async (): Promise<void> => {
    if (orderItems.length === 0) {
      setToastMessage('Sepetiniz boş. Lütfen ürün ekleyin.');
      return;
    }

    if (!selectedTable) {
      setToastMessage('Lütfen masa seçin.');
      return;
    }

    try {
      setIsLoading(true);

      // Cart datasını backend'in beklediği formata dönüştür
      const transformedCart = orderItems.map(item => {
        let price = 0;
        let selectedVariantId = null;
        const selectedAddonsId = [];

        // Variant seçildiyse variant fiyatını kullan
        if (item.selectedVariant) {
          selectedVariantId = item.selectedVariant.id;
          price = parseFloat(item.selectedVariant.price);
        } else {
          // Variant yoksa base item fiyatını kullan
          price = parseFloat(item.price);
        }

        // Addon'lar varsa addon fiyatlarını ekle
        if (item.selectedAddons && item.selectedAddons.length > 0) {
          item.selectedAddons.forEach((addon) => {
            selectedAddonsId.push(addon.id);
            const addonPrice = parseFloat(addon.price);
            price = price + addonPrice;
          });
        }

        return {
          id: item.id,
          title: item.title,
          price: price, // Hesaplanmış toplam fiyat
          quantity: item.quantity,
          variant_id: selectedVariantId ? selectedVariantId.toString() : null,
          addons_ids: selectedAddonsId.map(id => id.toString()),
          notes: null
        };
      });

      console.log('🛒 Transformed Cart:', JSON.stringify(transformedCart, null, 2));

      const response = await createOrder(transformedCart, 'dinein', customerType, customer?.id, selectedTable?.id);

      console.log('📦 Create Order Response:', response);

      // API'den dönen response'da orderId ve tokenNo varsa socket event'ini gönder
      if (response) {
      const { orderId, tokenNo } = response;


        if (orderId && tokenNo) {
          console.log('📡 Sending socket events...');
          sendNewOrderEvent(tokenNo, orderId);
        } else {
          console.log('⚠️ Missing orderId or tokenNo in response');
        }
      } else {
        console.log('⚠️ No data in response');
      }

      setToastMessage('Sipariş mutfağa başarıyla gönderildi!');
      setOrderItems([]);
      setSelectedTable(null);
      setIsCartVisible(false);

      // Sipariş oluşturduktan sonra verileri yenile
      await refreshOrders();
    } catch (error) {
      console.error('🔥 Create order error:', error);
      setToastMessage('Sipariş oluşturulamadı. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleIncrementItem = (item: CartItem): void => {
    setOrderItems(
      orderItems.map((orderItem) =>
        orderItem.id === item.id
          ? { ...orderItem, quantity: orderItem.quantity + 1 }
          : orderItem
      )
    );
  };

  const handleDecrementItem = (item: CartItem): void => {
    if (item.quantity > 1) {
      setOrderItems(
        orderItems.map((orderItem) =>
          orderItem.id === item.id
            ? { ...orderItem, quantity: orderItem.quantity - 1 }
            : orderItem
        )
      );
    }
  };

  const handleRemoveItem = (item: CartItem): void => {
    setOrderItems(orderItems.filter((orderItem) => orderItem.id !== item.id));
  };

  const renderCategory: ListRenderItem<Category> = ({ item }) => (
    <TouchableOpacity
      style={[styles.categoryButton, selectedCategory === item.title && styles.selectedCategory]}
      onPress={() => setSelectedCategory(item.title)}
      activeOpacity={0.7}
    >
      <Text style={[
        styles.categoryButtonText,
        selectedCategory === item.title && styles.selectedCategoryText
      ]}>
        {item.title}
      </Text>
    </TouchableOpacity>
  );


  if (isLoading) return <LoadingScreen message="POS verileri yükleniyor..." />;
  if (!posData) return <LoadingScreen message="Veri bulunamadı" />;

  const filteredItems = posData.menuItems.filter(item =>
    (selectedCategory === 'Tümü' || item.category_title === selectedCategory) &&
    item.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const floorsTableData = selectedFloor ? selectedFloor.tables : [];

  const filteredTableData = floorsTableData.filter(table => {
    if (tableFilter === 'all') return true;
    return table.table_status === tableFilter;
  });

  const renderContent = () => {
    if (!selectedTable) {
      return (
        <View style={styles.tableContainer}>
          
          {renderTableFilter()}
          <FlatList
            data={filteredTableData}
            renderItem={renderTable}
            keyExtractor={item => item.id.toString()}
            numColumns={2}
            style={styles.tableList}
            contentContainerStyle={styles.tableListContent}
            showsVerticalScrollIndicator={false}
          />
        </View>
      );
    }

    return (
      <>
        <TextInput
          style={styles.searchContainer}
          placeholder="  Ürün Ara..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />

        <FlatList
          horizontal
          data={[{ id: 'all', title: 'Tümü' }, ...posData.categories]}
          renderItem={renderCategory}
          keyExtractor={item => item.id.toString()}
          style={styles.categoriesList}
          showsHorizontalScrollIndicator={false}
        />

        <FlatList
          data={filteredItems}
          renderItem={({ item }) => (
            <MenuItem item={item} onPress={handleAddItem} />
          )}
          keyExtractor={item => item.id.toString()}
          numColumns={2}
          style={styles.menuList}
          showsVerticalScrollIndicator={false}
        />
      </>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.replace('Dashboard')}
          activeOpacity={0.7}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        
        {renderFloorButtons()}

        {/* QR Siparişleri Badge */}
        {qrOrdersCount > 0 && (
          <View style={styles.qrOrdersBadge}>
            <Icon name="qr-code" size={16} color="#fff" />
            <Text style={styles.qrOrdersText}>QR: {qrOrdersCount}</Text>
          </View>
        )}

        {selectedTable && (
          <View style={styles.selectedTableIndicator}>
            <Text style={styles.selectedTableText}>
              Masa: {selectedTable.table_title}
            </Text>
            <TouchableOpacity
              onPress={() => setSelectedTable(null)}
              style={styles.changeTableButton}
              activeOpacity={0.7}
            >
              <Text style={styles.changeTableButtonText}>Değiştir</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

     

      {renderContent()}

      {/* Sepet Butonu - Sadece ürün seçim ekranında göster, sepet açık değilken */}
      {(!selectedFloor || selectedTable) && !isCartVisible && (
        <TouchableOpacity
          style={styles.cartButton}
          onPress={() => setIsCartVisible(!isCartVisible)}
          activeOpacity={0.8}
        >
          <View style={styles.cartButtonContent}>
            <View style={styles.cartButtonLeft}>
              <Icon name="shopping-cart" size={24} color="#fff" />
              <Text style={styles.cartButtonText}>
                Sepet ({orderItems.length} ürün)
              </Text>
            </View>
            <View style={styles.cartButtonRight}>
              <Text style={styles.cartTotalText}>
                {formatCurrency(
                  orderItems.reduce((sum, item) => {
                    const itemPrice = item.totalPrice || parseFloat(item.price);
                    return sum + (itemPrice * item.quantity);
                  }, 0)
                )}
              </Text>
            </View>
          </View>
        </TouchableOpacity>
      )}

      {isCartVisible && (
        <CartModal
          items={orderItems}
          onClose={() => setIsCartVisible(false)}
          onCreateOrder={handleCreateOrder}
          onIncrementItem={handleIncrementItem}
          onDecrementItem={handleDecrementItem}
          onRemoveItem={handleRemoveItem}
          toastMessage={toastMessage}
        />
      )}

      {toastMessage && (
        <Toast
          message={toastMessage}
          duration={2000}
          type="success"
        />
      )}

      <TableModal
        visible={isTableModalVisible}
        table={selectedOccupiedTable}
        onClose={() => setIsTableModalVisible(false)}
        onPrintBill={() => {
          setIsTableModalVisible(false);
          setToastMessage('Hesap yazdırma özelliği yakında...');
        }}
        onViewItems={() => {
          setIsTableModalVisible(false);
          setIsTableItemsModalVisible(true);
        }}
        onAddProduct={() => {
          setIsTableModalVisible(false);
          if (selectedOccupiedTable) {
            setSelectedTable(selectedOccupiedTable);
            setToastMessage('Ürün ekleme moduna geçildi');
          }
        }}
      />

      <TableItemsModal
        visible={isTableItemsModalVisible}
        table={selectedOccupiedTable}
        onClose={() => setIsTableItemsModalVisible(false)}
      />

      <ItemCustomizationModal
        visible={isCustomizationModalVisible}
        item={selectedItemForCustomization}
        onClose={() => {
          setIsCustomizationModalVisible(false);
          setSelectedItemForCustomization(null);
        }}
        onAddToCart={addItemToCart}
      />

      {toastMessage ? (
        <Toast message={toastMessage} onHide={() => setToastMessage('')} />
      ) : null}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f7f7f7',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  backButton: {
    padding: 5,
  },
  qrOrdersBadge: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#FF9800',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
    gap: 6,
  },
  qrOrdersText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: 'bold',
  },
  tableContainer: {
    flex: 1,
  },
  floorsList: {
    maxHeight: 60,
    backgroundColor: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 10,
  },
  tableFilterContainer: {
    flexDirection: 'row',
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    padding: 10,
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f5f5f5',
    borderRadius: 15,
    minWidth: 80,
    justifyContent: 'center',
    marginLeft: 5,
  },
  selectedFilter: {
    backgroundColor: '#4CAF50',
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
  },
  selectedFilterText: {
    color: '#fff',
  },
  floorButton: {
    padding: 12,
    marginHorizontal: 5,
    backgroundColor: '#f0f0f0',
    minWidth: 100,
    alignItems: 'center',
    borderRadius: 8,
  },
  selectedFloor: {
    backgroundColor: '#4CAF50',
  },
  floorButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  selectedFloorText: {
    color: '#fff',
  },
  noFloorsText: {
    padding: 20,
    textAlign: 'center',
    fontSize: 16,
    color: '#777',
  },
  tableList: {
    flex: 1,
    padding: 5,
  },
  tableListContent: {
    paddingVertical: 5,
  },
  tableButton: {
    flex: 1,
    padding: 20,
    margin:5,  
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  availableTable: {
    backgroundColor: '#4CAF50', // Yeşil - Boş masa
  },
  occupiedTable: {
    backgroundColor: '#FFC107', // Sarı - Dolu masa
  },
  overdueTable: {
    backgroundColor: '#F44336', // Kırmızı - 10 dakika geçmiş
  },
  lockedTable: {
    backgroundColor: '#9E9E9E', // Gri - Kilitli masa
    opacity: 0.7,
  },
  tableTitle: {
    color: '#fff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
  },
  tableStatus: {
    color: '#fff',
    fontSize: 14,
  },
  tableInfoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 5,
    marginBottom: 4,
    width: '100%',
  },
  tableTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  tableOrderTime: {
    color: '#fff',
    fontSize: 11,
    fontWeight: '700',
  },
  tablePriceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 2,
  },
  tableTotalAmount: {
    color: '#fff',
    fontSize: 13,
    fontWeight: 'bold',
  },
  tableWaiterRow: {
    width: '100%',
    marginTop: 4,
  },
  tableWaiterContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    gap: 4,
  },
  tableWaiterName: {
    color: '#fff',
    fontSize: 10,
  },
  selectedTableIndicator: {
    flexDirection: 'row',
    backgroundColor: '#4CAF50',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 10,
    flex: 0.5,
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  selectedTableText: {
    color: '#fff',
    fontWeight: '600',
    fontSize: 16,
    flex: 1,
  },
  tableActions: {
    flexDirection: 'row',
    gap: 8,
  },
  changeTableButton: {
    backgroundColor: '#fff',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 12,
    alignItems: 'center',
    marginLeft: 10,
  },
  changeTableButtonText: {
    color: '#4CAF50',
    fontSize: 12,
    fontWeight: '600',
  },
  addProductButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 12,
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  addProductButtonText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  searchContainer: {
    padding: 15,
    marginVertical: 10,
    backgroundColor: '#fff',
    marginHorizontal: 15,
    borderRadius: 8,
    fontSize: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  categoriesList: {
    maxHeight: 60,
    backgroundColor: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 10,
    marginVertical: 5,
  },
  categoryButton: {
    padding: 12,
    marginHorizontal: 5,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
    minWidth: 80,
    alignItems: 'center',
  },
  selectedCategory: {
    backgroundColor: '#4CAF50',
  },
  categoryButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  selectedCategoryText: {
    color: '#fff',
  },
  menuList: {
    flex: 1,
    marginHorizontal: 10,
  },
  cartButton: {
    position: 'absolute',
    left: 20,
    right: 20,
    bottom: 20,
    backgroundColor: '#4CAF50',
    padding: 16,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    elevation: 8,
  },
  cartBadge: {
    position: 'absolute',
    top: -5,
    right: -5,
    backgroundColor: '#F44336',
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  cartButtonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  cartButtonLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  cartButtonRight: {
    alignItems: 'flex-end',
  },
  cartButtonText: {
    color: '#fff',
    marginLeft: 8,
    fontWeight: 'bold',
    fontSize: 16,
  },
  cartTotalText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 18,
  },

  // Modal Styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    margin: 20,
    maxWidth: 400,
    width: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalContent: {
    marginBottom: 20,
  },
  modalInfo: {
    fontSize: 16,
    color: '#666',
    marginBottom: 10,
    lineHeight: 24,
  },
  modalButtons: {
    gap: 12,
  },
  modalButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#666',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    gap: 8,
  },
  modalButtonSecondary: {
    backgroundColor: '#2196F3',
  },
  modalButtonPrimary: {
    backgroundColor: '#4CAF50',
  },
  modalButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default POSScreen;