import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  Alert
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { StackNavigationProp } from '@react-navigation/stack';
import { logout } from '../config/api';
import { RootStackParamList } from '../types';

type DashboardScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Dashboard'>;

interface DashboardScreenProps {
  navigation: DashboardScreenNavigationProp;
}

interface MenuItem {
  id: string;
  title: string;
  icon: string;
  color: string;
  onPress: () => void;
}

const DashboardScreen: React.FC<DashboardScreenProps> = ({ navigation }) => {
  const handleLogout = async (): Promise<void> => {
    Alert.alert(
      'Çıkış Yap',
      'Çıkış yapmak istediğinizden emin misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: '<PERSON><PERSON><PERSON><PERSON><PERSON> Yap',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              navigation.replace('Login');
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Çıkış Başarısız', 'Beklenmeyen bir hata oluştu');
            }
          },
        },
      ]
    );
  };

  const handleFeatureNotAvailable = (featureName: string): void => {
    Alert.alert('Bilgi', `${featureName} özelliği yakında eklenecek`);
  };

  const menuItems: MenuItem[] = [
    {
      id: 'pos',
      title: 'Satış Ekranı',
      icon: 'point-of-sale',
      color: '#4CAF50',
      onPress: () => navigation.navigate('Pos', {})
    },
    {
      id: 'reports',
      title: 'Raporlar',
      icon: 'assessment',
      color: '#2196F3',
      onPress: () => navigation.navigate('Reports')
    },
    {
      id: 'inventory',
      title: 'Stok Raporu',
      icon: 'inventory',
      color: '#FF9800',
      onPress: () => navigation.navigate('Inventory')
    },
    {
      id: 'menu-management',
      title: 'Menü Yönetimi',
      icon: 'restaurant-menu',
      color: '#9C27B0',
      onPress: () => navigation.navigate('MenuManagement')
    },
    {
      id: 'settings',
      title: 'Ayarlar',
      icon: 'settings',
      color: '#607D8B',
      onPress: () => handleFeatureNotAvailable('Ayarlar')
    },
    {
      id: 'customers',
      title: 'Müşteriler',
      icon: 'people',
      color: '#9C27B0',
      onPress: () => handleFeatureNotAvailable('Müşteriler')
    },
    {
      id: 'logout',
      title: 'Çıkış Yap',
      icon: 'logout',
      color: '#F44336',
      onPress: handleLogout
    }
  ];

  const renderMenuItem = (item: MenuItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.menuItem}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={[styles.menuIconContainer, { backgroundColor: item.color }]}>
        <Icon name={item.icon} size={32} color="#fff" />
      </View>
      <Text style={styles.menuItemText}>{item.title}</Text>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>HollyPOS</Text>
        <Text style={styles.headerSubtitle}>Yönetim Paneli</Text>
      </View>

      <View style={styles.welcomeContainer}>
        <View style={styles.welcomeContent}>
          <Text style={styles.welcomeText}>Hoş Geldiniz</Text>
          <Text style={styles.welcomeSubText}>Lütfen bir işlem seçin</Text>
        </View>
        <View style={styles.logoContainer}>
          <Icon name="storefront" size={60} color="#4CAF50" />
        </View>
      </View>

      <ScrollView
        style={styles.menuContainer}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.menuGrid}>
          {menuItems.map(renderMenuItem)}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4CAF50',
    padding: 20,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 5,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 4,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#fff',
    opacity: 0.9,
  },
  welcomeContainer: {
    backgroundColor: '#fff',
    margin: 15,
    borderRadius: 12,
    padding: 20,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  welcomeContent: {
    flex: 1,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  welcomeSubText: {
    fontSize: 16,
    color: '#666',
  },
  logoContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    flex: 1,
    padding: 15,
  },
  menuGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  menuItem: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  menuIconContainer: {
    width: 70,
    height: 70,
    borderRadius: 35,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
  },
});

export default DashboardScreen;
