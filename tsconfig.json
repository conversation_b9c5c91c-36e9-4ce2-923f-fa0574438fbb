{"extends": "@react-native/typescript-config/tsconfig.json", "compilerOptions": {"strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "exactOptionalPropertyTypes": true, "baseUrl": "./src", "paths": {"@components/*": ["components/*"], "@screens/*": ["screens/*"], "@navigation/*": ["navigation/*"], "@utils/*": ["utils/*"], "@config/*": ["config/*"], "@types/*": ["types/*"]}}, "include": ["src/**/*", "App.tsx", "index.js"], "exclude": ["node_modules", "android", "ios"]}