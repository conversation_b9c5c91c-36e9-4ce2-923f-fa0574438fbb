import React, { useState } from 'react';
import { View, Text, TouchableOpacity, FlatList, StyleSheet, Modal, ListRenderItem } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { Table, ModalProps } from '../types';

interface TableWithStatus extends Table {
  table_title: string;
  isOccupied: boolean;
}

interface TableSelectionModalProps extends ModalProps {
  onSelectTable: (table: TableWithStatus) => void;
  tables: TableWithStatus[];
}

const TableSelectionModal: React.FC<TableSelectionModalProps> = ({ 
  isVisible, 
  onClose, 
  onSelectTable, 
  tables 
}) => {
  const [selectedFloor, setSelectedFloor] = useState<number | null>(null);

  const floors = [...new Set(tables.map(table => table.floor))].sort((a, b) => a - b);

  const filteredTables = selectedFloor 
    ? tables.filter(table => table.floor === selectedFloor)
    : tables;

  const renderFloorItem: ListRenderItem<number> = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.floorButton, 
        selectedFloor === item && styles.selectedFloor
      ]}
      onPress={() => setSelectedFloor(selectedFloor === item ? null : item)}
      activeOpacity={0.7}
    >
      <Text style={[
        styles.floorButtonText,
        selectedFloor === item && styles.selectedFloorText
      ]}>
        {item}. Kat
      </Text>
    </TouchableOpacity>
  );

  const renderTableItem: ListRenderItem<TableWithStatus> = ({ item }) => (
    <TouchableOpacity
      style={[
        styles.tableButton,
        item.isOccupied ? styles.occupiedTable : styles.availableTable
      ]}
      onPress={() => onSelectTable(item)}
      disabled={item.isOccupied}
      activeOpacity={0.7}
    >
      <Text style={styles.tableButtonText}>{item.table_title}</Text>
      <Icon 
        name={item.isOccupied ? "event-busy" : "event-available"} 
        size={20} 
        color={item.isOccupied ? "#dc3545" : "#28a745"} 
      />
      <Text style={styles.tableStatusText}>
        {item.isOccupied ? 'Dolu' : 'Boş'}
      </Text>
    </TouchableOpacity>
  );

  return (
    <Modal
      animationType="slide"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <View style={styles.header}>
            <Text style={styles.modalTitle}>Masa Seçimi</Text>
            <TouchableOpacity onPress={onClose} style={styles.headerCloseButton}>
              <Icon name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>
          
          {/* Katlar arası seçim */}
          <View style={styles.floorSection}>
            <Text style={styles.sectionTitle}>Kat Seçimi:</Text>
            <FlatList
              horizontal
              data={floors}
              renderItem={renderFloorItem}
              keyExtractor={(item) => item.toString()}
              showsHorizontalScrollIndicator={false}
              contentContainerStyle={styles.floorList}
            />
          </View>

          {/* Masalar arası seçim */}
          <View style={styles.tableSection}>
            <Text style={styles.sectionTitle}>
              {selectedFloor ? `${selectedFloor}. Kat Masaları:` : 'Tüm Masalar:'}
            </Text>
            <FlatList
              data={filteredTables}
              renderItem={renderTableItem}
              keyExtractor={(item) => item.id.toString()}
              numColumns={3}
              showsVerticalScrollIndicator={false}
              contentContainerStyle={styles.tableList}
            />
          </View>

          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeButtonText}>Kapat</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingTop: 50,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
  },
  headerCloseButton: {
    padding: 5,
  },
  floorSection: {
    marginBottom: 20,
  },
  tableSection: {
    flex: 1,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 10,
  },
  floorList: {
    paddingHorizontal: 5,
  },
  floorButton: {
    backgroundColor: '#f0f0f0',
    padding: 12,
    marginHorizontal: 5,
    borderRadius: 25,
    borderWidth: 1,
    borderColor: '#ccc',
    minWidth: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  selectedFloor: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  floorButtonText: {
    color: '#333',
    fontWeight: '600',
    fontSize: 14,
  },
  selectedFloorText: {
    color: '#fff',
  },
  tableList: {
    paddingVertical: 10,
  },
  tableButton: {
    backgroundColor: '#e0e0e0',
    padding: 8,
    margin: 5,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
    width: 90,
    height: 90,
    borderWidth: 2,
    borderColor: '#ccc',
  },
  availableTable: {
    backgroundColor: '#d4edda',
    borderColor: '#28a745',
  },
  occupiedTable: {
    backgroundColor: '#f8d7da',
    borderColor: '#dc3545',
    opacity: 0.6,
  },
  tableButtonText: {
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
    fontSize: 12,
    textAlign: 'center',
  },
  tableStatusText: {
    fontSize: 10,
    color: '#666',
    marginTop: 2,
  },
  closeButton: {
    marginTop: 20,
    backgroundColor: '#4CAF50',
    padding: 15,
    borderRadius: 8,
    alignItems: 'center',
  },
  closeButtonText: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 16,
  },
});

export default TableSelectionModal;
