import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform
} from 'react-native';
import { Picker } from '@react-native-picker/picker';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { getMenuItemDetail, updateMenuItem, getCategories } from '../config/api';
import { RootStackParamList } from '../types';

type MenuItemEditScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MenuItemEdit'>;
type MenuItemEditScreenRouteProp = RouteProp<RootStackParamList, 'MenuItemEdit'>;

interface MenuItemEditScreenProps {
  navigation: MenuItemEditScreenNavigationProp;
  route: MenuItemEditScreenRouteProp;
}

interface Category {
  id: number;
  title: string;
}

interface MenuItemData {
  id: number;
  title: string;
  description: string | null;
  price: string;
  net_price: string | null;
  tax_id: number | null;
  category_id: number | null;
  category_title: string | null;
}

const MenuItemEditScreen: React.FC<MenuItemEditScreenProps> = ({ navigation, route }) => {
  const { itemId } = route.params;
  
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [isSaving, setIsSaving] = useState<boolean>(false);
  const [categories, setCategories] = useState<Category[]>([]);
  
  // Form fields
  const [title, setTitle] = useState<string>('');
  const [description, setDescription] = useState<string>('');
  const [price, setPrice] = useState<string>('');
  const [netPrice, setNetPrice] = useState<string>('');
  const [categoryId, setCategoryId] = useState<number | null>(null);
  const [allergens, setAllergens] = useState<string>('');

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const [menuItemData, categoriesData] = await Promise.all([
        getMenuItemDetail(itemId),
        getCategories()
      ]);
      
      // Menü öğesi verilerini form'a yükle
      const menuItem = menuItemData.formattedMenuItem;
      setTitle(menuItem.title || '');
      setDescription(menuItem.description || '');
      setPrice(menuItem.price || '');
      setNetPrice(menuItem.net_price || '');
      setCategoryId(menuItem.category_id);
      setAllergens(''); // API'de allergens bilgisi yok, boş bırakıyoruz
      
      setCategories(categoriesData || []);
    } catch (error) {
      console.error('Error fetching menu item data:', error);
      Alert.alert('Hata', 'Menü öğesi verileri alınamadı. Lütfen tekrar deneyin.');
      navigation.goBack();
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async (): Promise<void> => {
    // Validasyon
    if (!title.trim()) {
      Alert.alert('Hata', 'Ürün adı zorunludur.');
      return;
    }

    if (!price.trim()) {
      Alert.alert('Hata', 'Fiyat zorunludur.');
      return;
    }

    // Fiyat formatı kontrolü
    const priceNumber = parseFloat(price);
    if (isNaN(priceNumber) || priceNumber < 0) {
      Alert.alert('Hata', 'Geçerli bir fiyat giriniz.');
      return;
    }

    // Net fiyat kontrolü (varsa)
    if (netPrice.trim()) {
      const netPriceNumber = parseFloat(netPrice);
      if (isNaN(netPriceNumber) || netPriceNumber < 0) {
        Alert.alert('Hata', 'Geçerli bir net fiyat giriniz.');
        return;
      }
    }

    try {
      setIsSaving(true);
      
      const updateData = {
        title: title.trim(),
        description: description.trim() || undefined,
        price: price.trim(),
        netPrice: netPrice.trim() || undefined,
        taxId: undefined, // Şimdilik tax_id kullanmıyoruz
        categoryId: categoryId || undefined,
        allergens: allergens.trim() || undefined
      };

      await updateMenuItem(itemId, updateData);
      
      Alert.alert(
        'Başarılı',
        'Ürün başarıyla güncellendi.',
        [
          {
            text: 'Tamam',
            onPress: () => navigation.goBack()
          }
        ]
      );
    } catch (error) {
      console.error('Error updating menu item:', error);
      Alert.alert('Hata', 'Ürün güncellenemedi. Lütfen tekrar deneyin.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Ürün Düzenle</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Ürün bilgileri yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView 
        style={styles.container} 
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Ürün Düzenle</Text>
          <TouchableOpacity
            style={[styles.saveButton, isSaving && styles.saveButtonDisabled]}
            onPress={handleSave}
            disabled={isSaving}
          >
            {isSaving ? (
              <ActivityIndicator size="small" color="#fff" />
            ) : (
              <>
                <Icon name="save" size={20} color="#fff" />
                <Text style={styles.saveButtonText}>Kaydet</Text>
              </>
            )}
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.formContainer}>
            {/* Ürün Adı */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>
                Ürün Adı <Text style={styles.required}>*</Text>
              </Text>
              <TextInput
                style={styles.input}
                value={title}
                onChangeText={setTitle}
                placeholder="Ürün adını giriniz"
                placeholderTextColor="#999"
              />
            </View>

            {/* Açıklama */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Açıklama</Text>
              <TextInput
                style={[styles.input, styles.textArea]}
                value={description}
                onChangeText={setDescription}
                placeholder="Ürün açıklamasını giriniz"
                placeholderTextColor="#999"
                multiline
                numberOfLines={3}
                textAlignVertical="top"
              />
            </View>

            {/* Fiyat */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>
                Fiyat (₺) <Text style={styles.required}>*</Text>
              </Text>
              <TextInput
                style={styles.input}
                value={price}
                onChangeText={setPrice}
                placeholder="0.00"
                placeholderTextColor="#999"
                keyboardType="decimal-pad"
              />
            </View>

            {/* Net Fiyat */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Net Fiyat (₺)</Text>
              <TextInput
                style={styles.input}
                value={netPrice}
                onChangeText={setNetPrice}
                placeholder="0.00"
                placeholderTextColor="#999"
                keyboardType="decimal-pad"
              />
            </View>

            {/* Kategori */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Kategori</Text>
              <View style={styles.pickerContainer}>
                <Picker
                  selectedValue={categoryId}
                  onValueChange={(value) => setCategoryId(value)}
                  style={styles.picker}
                >
                  <Picker.Item label="Kategori seçiniz" value={null} />
                  {categories.map((category) => (
                    <Picker.Item
                      key={category.id}
                      label={category.title}
                      value={category.id}
                    />
                  ))}
                </Picker>
              </View>
            </View>

            {/* Alerjenler */}
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Alerjenler</Text>
              <TextInput
                style={styles.input}
                value={allergens}
                onChangeText={setAllergens}
                placeholder="Alerjen bilgilerini giriniz"
                placeholderTextColor="#999"
              />
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'center',
    marginHorizontal: 10,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#4CAF50',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
    gap: 5,
  },
  saveButtonDisabled: {
    backgroundColor: '#ccc',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  content: {
    flex: 1,
  },
  formContainer: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
  },
  required: {
    color: '#f44336',
  },
  input: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 12,
    fontSize: 16,
    color: '#333',
  },
  textArea: {
    height: 80,
    paddingTop: 12,
  },
  pickerContainer: {
    backgroundColor: '#fff',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
  },
  picker: {
    height: 50,
  },
});

export default MenuItemEditScreen;
