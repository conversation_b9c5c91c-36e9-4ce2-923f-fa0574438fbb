import axios, { AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Keychain from 'react-native-keychain';
import { ApiResponse, MenuItem, Order, User, Table, InventoryItem, SalesReport, CashRegisterData } from '../types';

const API_URL = 'https://backendpos.hollystone.com.tr/api/v1';

interface QueueItem {
  resolve: (value: any) => void;
  reject: (reason?: any) => void;
}

interface POSInitData {
  categories: any[];
  menuItems: MenuItem[];
  paymentTypes: any[];
  storeSettings: any;
  floors: any[];
}

interface OrderFilters {
  type?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  paymentStatus?: string;
  tableId?: number;
  customerId?: number;
}

interface CartItem {
  id: number;
  title: string;
  price: number;
  quantity: number;
}

interface Customer {
  id?: number;
  name: string;
  email?: string;
  phone?: string;
}

const apiClient = axios.create({
  baseURL: API_URL,
});

apiClient.interceptors.request.use(
  async (config: InternalAxiosRequestConfig) => {
    const token = await AsyncStorage.getItem('accessToken');
    if (token && config.headers) {
      config.headers['Authorization'] = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

let isRefreshing = false;
let failedQueue: QueueItem[] = [];

const processQueue = (error: any, token: string | null = null): void => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });

  failedQueue = [];
};

apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error) => {
    const originalRequest = error.config;

    if (error.response?.status === 401 && !originalRequest._retry) {
      if (isRefreshing) {
        return new Promise(function(resolve, reject) {
          failedQueue.push({resolve, reject});
        }).then(token => {
          if (originalRequest.headers) {
            originalRequest.headers['Authorization'] = 'Bearer ' + token;
          }
          return apiClient(originalRequest);
        }).catch(err => {
          return Promise.reject(err);
        });
      }

      originalRequest._retry = true;
      isRefreshing = true;

      return new Promise(function (resolve, reject) {
        refreshToken()
          .then(async (data: any) => {
            const { accessToken } = data;
            await AsyncStorage.setItem('accessToken', accessToken);
            if (apiClient.defaults.headers.common) {
              apiClient.defaults.headers.common['Authorization'] = 'Bearer ' + accessToken;
            }
            if (originalRequest.headers) {
              originalRequest.headers['Authorization'] = 'Bearer ' + accessToken;
            }
            processQueue(null, accessToken);
            resolve(apiClient(originalRequest));
          })
          .catch((err) => {
            processQueue(err, null);
            reject(err);
          })
          .finally(() => {
            isRefreshing = false;
          });
      });
    }

    return Promise.reject(error);
  }
);

export const getPOSData = async (): Promise<POSInitData> => {
  try {
    const response = await apiClient.get('/pos/init');
    const { categories, menuItems, paymentTypes, storeSettings, floors } = response.data;
    return { categories, menuItems, paymentTypes, storeSettings, floors };
  } catch (error) {
    console.error('Error fetching POS data:', error);
    throw error;
  }
};

export const getOrders = async (): Promise<Order[]> => {
  try {
    const res = await apiClient.get("/orders");
    return res.data;
  } catch (error) {
    console.error('Error fetching orders:', error);
    throw error;
  }
};

export const getOrdersInit = async (): Promise<any> => {
  try {
    const res = await apiClient.get("/orders/init");
   
    return res.data;
  } catch (error) {
    console.error('Error initializing orders:', error);
    throw error;
  }
};

export const updateKitchenOrderItemStatus = async (orderItemId: number, status: string): Promise<ApiResponse> => {
  try {
    const response = await apiClient.post(`/orders/update-status/${orderItemId}`, { status });
    return response.data;
  } catch (error) {
    console.error('Error updating order item status:', error);
    throw error;
  }
};

export const cancelKitchenOrder = async (orderIds: number[]): Promise<ApiResponse> => {
  try {
    const response = await apiClient.post(`/orders/cancel`, { orderIds });
    return response.data;
  } catch (error) {
    console.error('Error cancelling kitchen order:', error);
    throw error;
  }
};

export const completeKitchenOrder = async (orderIds: number[]): Promise<ApiResponse> => {
  try {
    const response = await apiClient.post(`/orders/complete`, { orderIds });
    return response.data;
  } catch (error) {
    console.error('Error completing kitchen order:', error);
    throw error;
  }
};

export const getCompleteOrderPaymentSummary = async (orderIds: number[]): Promise<any> => {
  try {
    const res = await apiClient.post("/orders/complete-order-payment-summary", { orderIds });
    return res.data;
  } catch (error) {
    console.error('Error getting order payment summary:', error);
    throw error;
  }
};

export const payAndCompleteKitchenOrder = async (
  orderIds: number[],
  subTotal: number,
  taxTotal: number,
  total: number
): Promise<ApiResponse> => {
  try {
    const response = await apiClient.post(`/orders/complete-and-pay-order`, {
      orderIds,
      subTotal,
      taxTotal,
      total
    });
    return response.data;
  } catch (error) {
    console.error('Error paying and completing kitchen order:', error);
    throw error;
  }
};

export const createOrder = async (
  cart: any[],
  deliveryType: string,
  customerType: string,
  customerId: number | null,
  tableId: number | null
): Promise<ApiResponse> => {
  try {
    const payload = {
      cart,
      deliveryType,
      customerType,
      customerId,
      tableId: tableId?.toString() || null,
      floorId: null,
      selectedQrOrderItem: null
    };

    console.log('API Payload:', JSON.stringify(payload, null, 2));

    const response = await apiClient.post('/pos/create-order', payload);
    return response.data;
  } catch (error) {
    console.error('Error creating order:', error);
    throw error;
  }
};

export const createOrderAndInvoice = async (
  cart: CartItem[],
  deliveryType: string,
  customerType: string,
  customer: Customer,
  tableId: number | null,
  itemsTotal: number,
  taxTotal: number,
  payableTotal: number,
  discountValue: number,
  discountType: string,
  paymentMethod: string
): Promise<ApiResponse> => {
  try {
    const response = await apiClient.post('/pos/create-order-and-invoice', {
      cart,
      deliveryType,
      customerType,
      customer,
      tableId,
      itemsTotal,
      taxTotal,
      payableTotal,
      discountValue,
      discountType,
      paymentMethod
    });
    return response.data;
  } catch (error) {
    console.error('Error creating order and invoice:', error);
    throw error;
  }
};

export const login = async (username: string, password: string): Promise<any> => {
  try {
    const response = await apiClient.post('/auth/signin', { username, password });
    await AsyncStorage.setItem('accessToken', response.data.accessToken);
    await Keychain.setGenericPassword(username, password);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const logout = async (): Promise<void> => {
  try {
    await apiClient.post('/auth/signout');
    await AsyncStorage.removeItem('accessToken');
    await Keychain.resetGenericPassword();
  } catch (error) {
    throw error;
  }
};

export const refreshToken = async (token?: string): Promise<any> => {
  try {
    const response = await apiClient.post('/auth/refresh-token', token ? { refreshToken: token } : {});
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const forgotPassword = async (email: string): Promise<ApiResponse> => {
  try {
    const response = await apiClient.post('/auth/forgot-password', { username: email });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const resetPassword = async (token: string, password: string): Promise<ApiResponse> => {
  try {
    const response = await apiClient.post(`/auth/reset-password/${token}`, { password });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const getReports = async (type: string, from: string | null = null, to: string | null = null): Promise<SalesReport> => {
  try {
    let url = `/reports?type=${type}`;

    if (type === 'custom' && from && to) {
      url += `&from=${from}&to=${to}`;
    }

    const response = await apiClient.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching reports:', error);
    throw error;
  }
};

export const getActiveCashRegisterSessions = async (): Promise<CashRegisterData[]> => {
  try {
    const response = await apiClient.get('/cash-register/cash-register-sessions?status=open');
    return response.data;
  } catch (error) {
    console.error('Error fetching active cash register sessions:', error);
    throw error;
  }
};

export const getLastCashRegisterSessions = async (): Promise<CashRegisterData[]> => {
  try {
    const response = await apiClient.get('/cash-register/last-sessions');
    return response.data;
  } catch (error) {
    console.error('Error fetching last cash register sessions:', error);
    throw error;
  }
};

export const getOrderDetails = async (filters: OrderFilters = {}): Promise<Order[]> => {
  try {
    // Filtre parametrelerini URL'ye ekle
    let url = '/order-details';
    const params = new URLSearchParams();

    if (filters.type) params.append('type', filters.type);
    if (filters.startDate) params.append('startDate', filters.startDate);
    if (filters.endDate) params.append('endDate', filters.endDate);
    if (filters.status) params.append('status', filters.status);
    if (filters.paymentStatus) params.append('paymentStatus', filters.paymentStatus);
    if (filters.tableId) params.append('tableId', filters.tableId.toString());
    if (filters.customerId) params.append('customerId', filters.customerId.toString());

    const queryString = params.toString();
    if (queryString) {
      url += `?${queryString}`;
    }

    const response = await apiClient.get(url);
    return response.data;
  } catch (error) {
    console.error('Error fetching order details:', error);
    throw error;
  }
};

export const getCashRegisterSessionById = async (id: number): Promise<CashRegisterData | null> => {
  if (!id) return null;

  try {
    const response = await apiClient.get(`/cash-register/cash-register-sessions/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching cash register session with id ${id}:`, error);
    throw error;
  }
};

export const getInventory = async (): Promise<InventoryItem[]> => {
  try {
    const response = await apiClient.get('/inventory');
    // API'den dönen veriyi kontrol et ve array olduğundan emin ol
    const data = response.data;
    return Array.isArray(data) ? data : [];
  } catch (error) {
    console.error('Error fetching inventory:', error);
    throw error;
  }
};

export const getDashboardStats = async (): Promise<any> => {
  try {
    const response: AxiosResponse<any> = await apiClient.get('/dashboard/stats');
    return response.data;
  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    throw error;
  }
};

// Envanter öğelerini getir
export const getInventoryItems = async (status = 'all') => {
  try {
    const response = await apiClient.get(`/inventory?status=${status}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching inventory items:', error);
    throw error;
  }
};

// Envanter öğesi ekle
export const addInventoryItem = async ({ title, quantity, unit, min_quantity_threshold }: any) => {
  try {
    const response = await apiClient.post('/inventory/add-item', {
      title,
      quantity,
      unit,
      min_quantity_threshold
    });
    return response.data;
  } catch (error) {
    console.error('Error adding inventory item:', error);
    throw error;
  }
};

// Envanter öğesi güncelle
export const updateInventoryItem = async ({ id, title, unit, min_quantity_threshold }: any) => {
  try {
    const response = await apiClient.put(`/inventory/${id}`, {
      title,
      unit,
      min_quantity_threshold
    });
    return response.data;
  } catch (error) {
    console.error('Error updating inventory item:', error);
    throw error;
  }
};

// Stok hareketi ekle
export const addInventoryItemStockMovement = async ({ id, movementType, quantity, note }: any) => {
  try {
    const response = await apiClient.patch(`/inventory/${id}/add-stock-movement`, {
      movementType,
      quantity,
      note
    });
    return response.data;
  } catch (error) {
    console.error('Error adding stock movement:', error);
    throw error;
  }
};

// Envanter öğesi sil
export const deleteInventoryItem = async (id: number) => {
  try {
    const response = await apiClient.delete(`/inventory/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error deleting inventory item:', error);
    throw error;
  }
};

// Envanter logları getir
export const getInventoryLogs = async ({ type, from = null, to = null, movementType = null }: any) => {
  try {
    const params = new URLSearchParams();
    params.append('type', type);
    if (from) params.append('from', from);
    if (to) params.append('to', to);
    if (movementType) params.append('movementType', movementType);

    const response = await apiClient.get(`/inventory/logs?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching inventory logs:', error);
    throw error;
  }
};

// Envanter dashboard verilerini getir
export const getInventoryDashboard = async ({ type, from = null, to = null }: any) => {
  try {
    const params = new URLSearchParams();
    params.append('type', type);
    if (from) params.append('from', from);
    if (to) params.append('to', to);

    const response = await apiClient.get(`/inventory/dashboard?${params.toString()}`);
    return response.data;
  } catch (error) {
    console.error('Error fetching inventory dashboard:', error);
    throw error;
  }
};

// Tüm kasa oturumlarını getir
export const getAllCashRegisterSessions = async (): Promise<any> => {
  try {
    const response = await apiClient.get('/cash-register/cash-register-sessions');
    return response.data;
  } catch (error) {
    console.error('Tüm kasa oturumları alınırken hata:', error);
    throw error;
  }
};
