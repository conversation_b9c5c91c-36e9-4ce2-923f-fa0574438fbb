import React from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { formatTurkishTime, formatCurrency } from '../utils/dateUtils';

interface Order {
  id: number;
  date: string;
  delivery_type: string;
  customer_type: string;
  table_id: number;
  table_title: string;
  status: string;
  payment_status: string;
  token_no: number;
  username: string;
  user_name: string;
  items: any[];
  partialPayments: any[];
}

interface Table {
  id: number;
  encrypted_id: string;
  table_title: string;
  table_status: 'empty' | 'busy' | 'locked';
  floor: number;
  seating_capacity: number;
  orders: Order[];
  order_ids: number[];
}

interface TableModalProps {
  visible: boolean;
  table: Table | null;
  onClose: () => void;
  onPrintBill: () => void;
  onViewItems: () => void;
  onAddProduct: () => void;
}

const TableModal: React.FC<TableModalProps> = ({
  visible,
  table,
  onClose,
  onPrintBill,
  onViewItems,
  onAddProduct
}) => {
  if (!table) return null;



  const getLatestOrder = (table: Table): Order | null => {
    if (table.orders.length === 0) return null;
    return table.orders[table.orders.length - 1] || null;
  };

  const getTableTotalAmount = (table: Table): number => {
    return table.orders.reduce((total, order) => {
      return total + order.items.reduce((orderTotal, item) => {
        return orderTotal + (parseFloat(item.price) * item.quantity);
      }, 0);
    }, 0);
  };

  return (
    <Modal
      visible={visible}
      transparent={true}
      animationType="fade"
      onRequestClose={onClose}
    >
      <View style={styles.modalOverlay}>
        <View style={styles.modalContainer}>
          <View style={styles.modalHeader}>
            <View style={styles.modalTitleContainer}>
              <Text style={styles.modalTitle}>
                {table.table_title}
              </Text>
              {table.order_ids.length > 0 && (
                <Text style={styles.modalSubtitle}>
                  Fiş No: #{table.order_ids.join(', #')}
                </Text>
              )}
            </View>
            <TouchableOpacity
              onPress={onClose}
              style={styles.modalCloseButton}
            >
              <Icon name="close" size={24} color="#666" />
            </TouchableOpacity>
          </View>

          <View style={styles.modalContent}>
            {(() => {
              const latestOrder = getLatestOrder(table);
              const totalAmount = getTableTotalAmount(table);

              return (
                <>
                  <View style={styles.modalMainInfo}>
                    <View style={styles.modalTimeContainer}>
                      <Text style={styles.modalTimeText}>
                        {latestOrder ? formatTurkishTime(latestOrder.date) : '--:--'}
                      </Text>
                    </View>

                    <View style={styles.modalAmountContainer}>
                      <Text style={styles.modalAmountText}>
                        {formatCurrency(totalAmount)}
                      </Text>
                    </View>
                  </View>

                  {latestOrder && (
                    <View style={styles.modalUserInfo}>
                      <Text style={styles.modalUserText}>
                        {latestOrder.user_name}
                      </Text>
                    </View>
                  )}
                </>
              );
            })()}
          </View>

          <View style={styles.modalButtons}>
            <View style={styles.buttonRow}>
              <TouchableOpacity
                style={[styles.modalButton, styles.printButton]}
                onPress={onPrintBill}
              >
                <Icon name="print" size={32} color="#666" />
                <Text style={styles.modalButtonText}>Hesap Yazdır</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.modalButton, styles.viewButton]}
                onPress={onViewItems}
              >
                <Icon name="list" size={32} color="#2196F3" />
                <Text style={[styles.modalButtonText, styles.viewButtonText]}>Ürünleri Gör</Text>
              </TouchableOpacity>
            </View>

            <TouchableOpacity
              style={[styles.modalButton, styles.addButton]}
              onPress={onAddProduct}
            >
              <Icon name="add" size={32} color="#fff" />
              <Text style={styles.addButtonText}>Ürün Ekle</Text>
            </TouchableOpacity>
          </View>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    margin: 20,
    maxWidth: 400,
    width: '90%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  modalTitleContainer: {
    flex: 1,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  modalSubtitle: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  modalCloseButton: {
    padding: 5,
  },
  modalContent: {
    marginBottom: 20,
  },
  modalMainInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    backgroundColor: '#f9f9f9',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
  },
  modalTimeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  modalTimeText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  modalAmountContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  modalAmountText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  modalUserInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingHorizontal: 15,
  },
  modalUserText: {
    fontSize: 14,
    color: '#666',
  },
  modalButtons: {
    gap: 15,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 15,
  },
  modalButton: {
    flex: 1,
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
    paddingVertical: 20,
    paddingHorizontal: 15,
    borderRadius: 12,
    gap: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  printButton: {
    backgroundColor: '#f9f9f9',
  },
  viewButton: {
    backgroundColor: '#f0f8ff',
    borderColor: '#2196F3',
  },
  addButton: {
    backgroundColor: '#4CAF50',
    borderColor: '#4CAF50',
  },
  modalButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    textAlign: 'center',
  },
  viewButtonText: {
    color: '#2196F3',
  },
  addButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default TableModal;
