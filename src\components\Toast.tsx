import React, { useEffect, useState } from 'react';
import { View, Text, Animated, StyleSheet } from 'react-native';

interface ToastProps {
  message?: string;
  duration?: number;
  type?: 'success' | 'error' | 'warning' | 'info';
}

const Toast: React.FC<ToastProps> = ({ message, duration = 2000, type = 'success' }) => {
  const [fadeAnim] = useState(new Animated.Value(0));

  useEffect(() => {
    if (!message) return;

    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 500,
      useNativeDriver: true,
    }).start();

    const timer = setTimeout(() => {
      Animated.timing(fadeAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true,
      }).start();
    }, duration);

    return () => clearTimeout(timer);
  }, [fadeAnim, message, duration]);

  if (!message) return null;

  const getBackgroundColor = (): string => {
    switch (type) {
      case 'success':
        return '#4CAF50';
      case 'error':
        return '#F44336';
      case 'warning':
        return '#FF9800';
      case 'info':
        return '#2196F3';
      default:
        return '#4CAF50';
    }
  };

  const getTextColor = (): string => {
    return '#FFFFFF';
  };

  return (
    <Animated.View 
      style={[
        styles.toastContainer, 
        { 
          opacity: fadeAnim,
          backgroundColor: getBackgroundColor()
        }
      ]}
    >
      <Text style={[styles.toastText, { color: getTextColor() }]}>
        {message}
      </Text>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  toastContainer: {
    position: 'absolute',
    top: 30,
    left: '50%',
    transform: [{ translateX: -150 }],
    width: 300,
    paddingVertical: 15,
    paddingHorizontal: 20,
    borderRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 5,
    alignItems: 'center',
    zIndex: 1000,
  },
  toastText: {
    fontSize: 16,
    fontWeight: '600',
    textAlign: 'center',
  },
});

export default Toast;
