import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { CartItem } from '../types';

interface OrderSummaryProps {
  items: CartItem[];
  onIncrementItem: (item: CartItem) => void;
  onDecrementItem: (item: CartItem) => void;
  onRemoveItem: (item: CartItem) => void;
}

const OrderSummary: React.FC<OrderSummaryProps> = ({ 
  items, 
  onIncrementItem, 
  onDecrementItem, 
  onRemoveItem 
}) => {
  const total = items.reduce((sum, item) => sum + item.price * item.quantity, 0);

  return (
    <View style={styles.container}>
      {items.length === 0 ? (
        <Text style={styles.emptyMessage}>Henüz ürün eklenmedi.</Text>
      ) : (
        items.map((item, index) => (
          <View key={`${item.id}-${index}`} style={styles.item}>
            <View style={styles.itemDetails}>
              <Text style={styles.itemTitle}>{item.title}</Text>
              <Text style={styles.itemPrice}>
                {(item.price * item.quantity).toFixed(2)} TRY
              </Text>
            </View>
            <View style={styles.itemActions}>
              <TouchableOpacity 
                onPress={() => onDecrementItem(item)} 
                disabled={item.quantity === 1}
                style={styles.actionButton}
              >
                <Icon 
                  name="remove" 
                  size={24} 
                  color={item.quantity === 1 ? '#ccc' : '#000'} 
                />
              </TouchableOpacity>
              <Text style={styles.itemQuantity}>{item.quantity}</Text>
              <TouchableOpacity 
                onPress={() => onIncrementItem(item)}
                style={styles.actionButton}
              >
                <Icon name="add" size={24} color="#000" />
              </TouchableOpacity>
              <TouchableOpacity 
                onPress={() => onRemoveItem(item)}
                style={styles.actionButton}
              >
                <Icon name="delete" size={24} color="#F44336" />
              </TouchableOpacity>
            </View>
          </View>
        ))
      )}

      {items.length > 0 && (
        <View style={styles.totalContainer}>
          <Text style={styles.totalText}>Toplam:</Text>
          <Text style={styles.totalAmount}>{total.toFixed(2)} TRY</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 10,
    marginVertical: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  emptyMessage: {
    fontSize: 16,
    color: '#888',
    textAlign: 'center',
    paddingVertical: 20,
    fontStyle: 'italic',
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  itemDetails: {
    flexDirection: 'column',
    flex: 1,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  itemPrice: {
    fontSize: 14,
    color: '#666',
    marginTop: 3,
  },
  itemActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: 4,
  },
  itemQuantity: {
    marginHorizontal: 10,
    fontSize: 16,
    fontWeight: '600',
    minWidth: 30,
    textAlign: 'center',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingTop: 10,
    borderTopWidth: 2,
    borderTopColor: '#4CAF50',
  },
  totalText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  totalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
});

export default OrderSummary;
