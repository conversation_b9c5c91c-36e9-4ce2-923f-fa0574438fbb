import React from 'react';
import { TouchableOpacity, Text, StyleSheet, View } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { MenuItem as MenuItemType } from '../types';

interface MenuItemProps {
  item: MenuItemType;
  onPress: (item: MenuItemType) => void;
}

const MenuItem: React.FC<MenuItemProps> = ({ item, onPress }) => {
  const hasVariants = item.variants && item.variants.length > 0;
  const hasAddons = item.addons && item.addons.length > 0;
  const hasCustomizations = hasVariants || hasAddons;

  const formatCurrency = (price: string): string => {
    return `${parseFloat(price).toFixed(2)} ₺`;
  };

  return (
    <TouchableOpacity style={styles.menuItem} onPress={() => onPress(item)}>
      <View style={styles.itemHeader}>
        <Text style={styles.menuItemTitle}>{item.title}</Text>
        {hasCustomizations && (
          <Icon name="tune" size={16} color="#FF9800" />
        )}
      </View>
      <Text style={styles.menuItemPrice}>{formatCurrency(item.price)}</Text>
      <Text style={styles.menuItemCategory}>{item.category_title}</Text>

      {hasCustomizations && (
        <View style={styles.customizationInfo}>
          {hasVariants && (
            <Text style={styles.customizationText}>
              {item.variants!.length} varyasyon
            </Text>
          )}
          {hasAddons && (
            <Text style={styles.customizationText}>
              {item.addons!.length} ekstra
            </Text>
          )}
        </View>
      )}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  menuItem: {
    flex: 1,
    margin: 5,
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  menuItemPrice: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '600',
    marginBottom: 2,
  },
  menuItemCategory: {
    fontSize: 12,
    color: '#888',
    marginBottom: 4,
  },
  customizationInfo: {
    flexDirection: 'row',
    gap: 8,
  },
  customizationText: {
    fontSize: 10,
    color: '#FF9800',
    backgroundColor: '#FFF3E0',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    fontWeight: '500',
  },
});

export default MenuItem;
