import React from 'react';
import { TouchableOpacity, Text, StyleSheet } from 'react-native';
import { MenuItem as MenuItemType } from '../types';

interface MenuItemProps {
  item: MenuItemType;
  onPress: (item: MenuItemType) => void;
}

const MenuItem: React.FC<MenuItemProps> = ({ item, onPress }) => (
  <TouchableOpacity style={styles.menuItem} onPress={() => onPress(item)}>
    <Text style={styles.menuItemTitle}>{item.title}</Text>
    <Text style={styles.menuItemPrice}>{item.price} TRY</Text>
    <Text style={styles.menuItemCategory}>{item.category_title}</Text>
  </TouchableOpacity>
);

const styles = StyleSheet.create({
  menuItem: {
    flex: 1,
    margin: 5,
    padding: 10,
    backgroundColor: '#fff',
    borderRadius: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 4,
  },
  menuItemPrice: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: '600',
    marginBottom: 2,
  },
  menuItemCategory: {
    fontSize: 12,
    color: '#888',
  },
});

export default MenuItem;
