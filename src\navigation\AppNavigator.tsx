import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import LoginScreen from '../screens/LoginScreen';
import MainScreen from '../screens/MainScreen';
import POSScreen from '../screens/POSScreen';
import ReportsScreen from '../screens/ReportsScreen';
import InventoryScreen from '../screens/InventoryScreen';
import InventoryDashboardScreen from '../screens/InventoryDashboardScreen';
import MenuManagementScreen from '../screens/MenuManagementScreen';
import MenuItemEditScreen from '../screens/MenuItemEditScreen';
import DashboardScreen from '../screens/DashboardScreen';
import CashRegisterDetailScreen from '../screens/CashRegisterDetailScreen';
import OpenOrdersDetailScreen from '../screens/OpenOrdersDetailScreen';
import CancelledOrdersDetailScreen from '../screens/CancelledOrdersDetailScreen';
import WasteDetailScreen from '../screens/WasteDetailScreen';
import ComplimentaryDetailScreen from '../screens/ComplimentaryDetailScreen';
import DiscountDetailScreen from '../screens/DiscountDetailScreen';
import CreditDetailScreen from '../screens/CreditDetailScreen';
import OrderDetailsScreen from '../screens/OrderDetailsScreen';
import SingleOrderDetailScreen from '../screens/SingleOrderDetailScreen';
import WaiterPerformanceScreen from '../screens/WaiterPerformanceScreen';
import { RootStackParamList } from '../types';

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Login"
        screenOptions={{
          headerShown: false,
          gestureEnabled: false,
          cardStyleInterpolator: ({ current, layouts }) => {
            return {
              cardStyle: {
                transform: [
                  {
                    translateX: current.progress.interpolate({
                      inputRange: [0, 1],
                      outputRange: [layouts.screen.width, 0],
                    }),
                  },
                ],
              },
            };
          },
        }}
      >
        <Stack.Screen
          name="Login"
          component={LoginScreen}
          options={{
            headerShown: false,
            gestureEnabled: false
          }}
        />
        <Stack.Screen
          name="Dashboard"
          component={DashboardScreen}
          options={{
            headerShown: false,
            gestureEnabled: false
          }}
        />
        <Stack.Screen
          name="Main"
          component={MainScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Pos"
          component={POSScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Reports"
          component={ReportsScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="Inventory"
          component={InventoryScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="InventoryDashboard"
          component={InventoryDashboardScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="MenuManagement"
          component={MenuManagementScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="MenuItemEdit"
          component={MenuItemEditScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="CashRegisterDetail"
          component={CashRegisterDetailScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="OpenOrdersDetail"
          component={OpenOrdersDetailScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="CancelledOrdersDetail"
          component={CancelledOrdersDetailScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="WasteDetail"
          component={WasteDetailScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="ComplimentaryDetail"
          component={ComplimentaryDetailScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="DiscountDetail"
          component={DiscountDetailScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="CreditDetail"
          component={CreditDetailScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="OrderDetails"
          component={OrderDetailsScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="SingleOrderDetail"
          component={SingleOrderDetailScreen}
          options={{ headerShown: false }}
        />
        <Stack.Screen
          name="WaiterPerformance"
          component={WaiterPerformanceScreen}
          options={{ headerShown: false }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

export default AppNavigator;
