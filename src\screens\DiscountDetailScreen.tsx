import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { getReports } from '../config/api';
import { formatCurrency, formatTurkishDateTime } from '../utils/dateUtils';

type NavigationProp = StackNavigationProp<RootStackParamList, 'DiscountDetail'>;
type RoutePropType = RouteProp<RootStackParamList, 'DiscountDetail'>;

const DiscountDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RoutePropType>();
  const [reportData, setReportData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async (): Promise<void> => {
    try {
      setLoading(true);
      const { reportType, startDate, endDate } = route.params;

      let response;
      if (reportType === 'custom' && startDate && endDate) {
        response = await getReports(reportType, startDate, endDate);
      } else {
        response = await getReports(reportType);
      }

      setReportData(response);
    } catch (error) {
      console.error('Error fetching report data:', error);
      setReportData(null);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async (): Promise<void> => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const getDeliveryTypeTitle = (type: string): string => {
    switch (type) {
      case 'dinein': return 'Masa Siparişi';
      case 'takeaway': return 'Paket Servis';
      case 'delivery': return 'Teslimat';
      default: return 'Bilinmeyen';
    }
  };

  const getDiscountTypeTitle = (type: string): string => {
    switch (type) {
      case 'percentage': return 'Yüzde İndirimi';
      case 'amount': return 'Tutar İndirimi';
      default: return 'İndirim';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>İndirim Detayları</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>İndirim Detayları</Text>
      </View>

      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Icon name="local-offer" size={24} color="#E91E63" />
          <Text style={styles.summaryLabel}>Toplam İndirim</Text>
          <Text style={styles.summaryValue}>
            {formatCurrency(
              reportData?.getTotalDiscounts && typeof reportData.getTotalDiscounts === 'object'
                ? typeof (reportData.getTotalDiscounts as any).total === 'string'
                  ? parseFloat((reportData.getTotalDiscounts as any).total)
                  : (reportData.getTotalDiscounts as any).total
                : reportData?.getTotalDiscounts || 0
            )}
          </Text>
        </View>
        <View style={styles.summaryCard}>
          <Icon name="list-alt" size={24} color="#FF5722" />
          <Text style={styles.summaryLabel}>İndirim Sayısı</Text>
          <Text style={styles.summaryValue}>
            {reportData?.getTotalDiscounts && typeof reportData.getTotalDiscounts === 'object' && (reportData.getTotalDiscounts as any).details
              ? (reportData.getTotalDiscounts as any).details.length
              : 0
            }
          </Text>
        </View>
      </View>

      <ScrollView
        style={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {reportData?.getTotalDiscounts && typeof reportData.getTotalDiscounts === 'object' && (reportData.getTotalDiscounts as any).details && (reportData.getTotalDiscounts as any).details.length > 0 ? (
          <View style={styles.sectionContainer}>
            {(reportData.getTotalDiscounts as any).details.map((item: any, index: number) => (
              <View key={index} style={styles.orderItem}>
                <View style={styles.orderHeader}>
                  <Text style={styles.orderNumber}>#{item.order_id}</Text>
                  <Text style={styles.orderTotal}>{formatCurrency(parseFloat(item.calculated_discount))}</Text>
                </View>

                <View style={styles.discountInfo}>
                  <View style={styles.discountTypeContainer}>
                    <Icon
                      name={item.discount_type === 'percentage' ? 'percent' : 'attach-money'}
                      size={16}
                      color="#E91E63"
                    />
                    <Text style={styles.discountType}>
                      {getDiscountTypeTitle(item.discount_type)}
                    </Text>
                  </View>
                  <Text style={styles.discountValue}>
                    {item.discount_type === 'percentage'
                      ? `%${item.discount_value}`
                      : formatCurrency(parseFloat(item.discount_value))
                    }
                  </Text>
                </View>

                <View style={styles.orderInfo}>
                  <View style={styles.infoRow}>
                    <Icon name="restaurant" size={16} color="#666" />
                    <Text style={styles.infoText}>{item.table_title}</Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Icon name="access-time" size={16} color="#666" />
                    <Text style={styles.infoText}>{formatTurkishDateTime(item.order_date)}</Text>
                  </View>
                </View>

                <View style={styles.orderInfo}>
                  <View style={styles.infoRow}>
                    <Icon name="local-shipping" size={16} color="#666" />
                    <Text style={styles.infoText}>{getDeliveryTypeTitle(item.delivery_type)}</Text>
                  </View>
                  <View style={styles.infoRow}>
                    <Icon name="person-outline" size={16} color="#666" />
                    <Text style={styles.infoText}>{item.user_name}</Text>
                  </View>
                </View>

                <View style={styles.processInfo}>
                  <Icon name="schedule" size={16} color="#666" />
                  <Text style={styles.processText}>
                    İşlem Zamanı: {formatTurkishDateTime(item.created_at)}
                  </Text>
                </View>

                {item.item_title && (
                  <View style={styles.itemInfo}>
                    <Icon name="shopping-cart" size={16} color="#666" />
                    <Text style={styles.itemText}>Ürün: {item.item_title}</Text>
                  </View>
                )}
              </View>
            ))}
          </View>
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="local-offer" size={60} color="#E91E63" />
            <Text style={styles.emptyText}>İndirim bulunmuyor</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingTop: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  summaryContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  scrollContainer: {
    flex: 1,
  },
  sectionContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  orderItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#E91E63',
  },
  discountInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
    paddingVertical: 8,
    paddingHorizontal: 12,
    backgroundColor: '#f8f9fa',
    borderRadius: 6,
  },
  discountTypeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  discountType: {
    fontSize: 14,
    color: '#333',
    marginLeft: 6,
    fontWeight: '500',
  },
  discountValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#E91E63',
  },
  orderInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  infoText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  processInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  processText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
  },
  itemInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#e8f5e8',
    padding: 8,
    borderRadius: 4,
  },
  itemText: {
    fontSize: 12,
    color: '#4CAF50',
    marginLeft: 4,
    fontWeight: '500',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 10,
    textAlign: 'center',
  },
});

export default DiscountDetailScreen;
