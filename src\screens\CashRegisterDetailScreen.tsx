import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { getCashRegisterSessionById } from '../config/api';
import { formatTurkishDateTime, formatTurkishDate, formatTurkishTime, formatCurrency } from '../utils/dateUtils';
import { RootStackParamList } from '../types';

type CashRegisterDetailScreenNavigationProp = StackNavigationProp<RootStackParamList, 'CashRegisterDetail'>;
type CashRegisterDetailScreenRouteProp = RouteProp<RootStackParamList, 'CashRegisterDetail'>;

interface CashRegisterDetailScreenProps {
  navigation: CashRegisterDetailScreenNavigationProp;
  route: CashRegisterDetailScreenRouteProp;
}

interface SessionData {
  session: any;
  transactions: any[];
}

const CashRegisterDetailScreen: React.FC<CashRegisterDetailScreenProps> = ({
  navigation,
  route
}) => {
  const { sessionId } = route.params || {};
  const [session, setSession] = useState<SessionData | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [transactionFilter, setTransactionFilter] = useState<string>('all');

  useEffect(() => {
    if (sessionId) {
      fetchSessionData();
    } else {
      setError('Geçersiz oturum ID');
      setIsLoading(false);
    }
  }, [sessionId]);

  const fetchSessionData = async (): Promise<void> => {
    if (!sessionId) {
      setError('Kasa oturumu ID\'si bulunamadı');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      const response = await getCashRegisterSessionById(sessionId);

      // API yanıtı farklı formatlarda gelebilir, bunu kontrol edelim
      if (response && (response as any).success && (response as any).data && (response as any).data.session) {
        // Yeni API formatı: { success: true, data: { session: {...}, transactions: [...] } }
        setSession((response as any).data);
      } else if (response && typeof response === 'object') {
        // Eski format: doğrudan session objesi
        setSession({ session: response, transactions: [] });
      } else {
        throw new Error('Beklenmeyen API yanıt formatı');
      }
    } catch (error) {
      console.error('Error fetching session data:', error);
      setError('Kasa oturumu verileri alınamadı. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
    }
  };



  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
          >
            <Icon name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Kasa Detayı</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#4CAF50" />
          <Text style={styles.loadingText}>Kasa verileri yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error || !session || !session.session) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
            activeOpacity={0.7}
          >
            <Icon name="arrow-back" size={24} color="#fff" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Kasa Detayı</Text>
        </View>
        <View style={styles.errorContainer}>
          <Icon name="error" size={64} color="#F44336" />
          <Text style={styles.errorText}>{error || 'Veri bulunamadı'}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={fetchSessionData}
            activeOpacity={0.7}
          >
            <Text style={styles.retryButtonText}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  // API yanıtından session ve transactions verilerini alalım
  const { session: sessionData, transactions } = session;

  const renderSectionTitle = (title: string) => (
    <View style={styles.sectionTitleContainer}>
      <Text style={styles.sectionTitle}>{title}</Text>
    </View>
  );

  const renderInfoRow = (label: string, value: string) => (
    <View style={styles.infoRow}>
      <Text style={styles.infoLabel}>{label}</Text>
      <Text style={styles.infoValue}>{value}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Kasa Oturumu Detayı</Text>
      </View>

      <ScrollView style={styles.scrollContainer}>
        <View style={styles.sessionHeader}>
          <Icon
            name="point-of-sale"
            size={30}
            color={sessionData.status === 'open' ? '#4CAF50' : '#FF9800'}
          />
          <View style={styles.sessionHeaderInfo}>
            <Text style={styles.sessionName}>{sessionData.register_name || 'İsimsiz Kasa'}</Text>
            <Text style={styles.sessionStatus}>
              {sessionData.status === 'open' ? 'Açık Oturum' : 'Kapalı Oturum'}
            </Text>
          </View>
        </View>

        {renderSectionTitle('Genel Bilgiler')}
        <View style={styles.infoCard}>
          {renderInfoRow('Açılış Zamanı', formatTurkishDateTime(sessionData.opened_at))}
          {sessionData.closed_at && renderInfoRow('Kapanış Zamanı', formatTurkishDateTime(sessionData.closed_at))}
          {renderInfoRow('Açan Kullanıcı', sessionData.opener_name || '-')}
          {sessionData.closer_name && renderInfoRow('Kapatan Kullanıcı', sessionData.closer_name)}
          {renderInfoRow('Açılış Tutarı', formatCurrency(sessionData.opening_amount))}
          {sessionData.closing_amount > 0 && renderInfoRow('Kapanış Tutarı', formatCurrency(sessionData.closing_amount))}
          {sessionData.expected_amount && renderInfoRow('Beklenen Tutar', formatCurrency(sessionData.expected_amount))}
          {sessionData.difference_amount && renderInfoRow('Fark', formatCurrency(sessionData.difference_amount))}
          {renderInfoRow('Kasa Kalan', formatCurrency(sessionData.total_transactions))}
          {sessionData.register_location && renderInfoRow('Konum', sessionData.register_location)}
          {sessionData.opening_notes && renderInfoRow('Açılış Notları', sessionData.opening_notes)}
          {sessionData.closing_notes && renderInfoRow('Kapanış Notları', sessionData.closing_notes)}
        </View>

        {sessionData.payment_totals_by_type && sessionData.payment_totals_by_type.length > 0 && (
          <>
            {renderSectionTitle('Ödeme Türleri')}
            <View style={styles.infoCard}>
              {sessionData.payment_totals_by_type.map((payment: any, index: number) => (
                <View key={index} style={styles.paymentItem}>
                  <Text style={styles.paymentName}>{payment.payment_type_name || 'Bilinmiyor'}</Text>
                  <Text style={styles.paymentAmount}>{formatCurrency(payment.total_amount)}</Text>
                </View>
              ))}
            </View>
          </>
        )}

        {sessionData.withdrawal_totals_by_type && sessionData.withdrawal_totals_by_type.length > 0 && (
          <>
            {renderSectionTitle('Para Çıkış İşlemleri')}
            <View style={styles.infoCard}>
              {sessionData.withdrawal_totals_by_type.map((withdrawal: any, index: number) => (
                <View key={index} style={styles.paymentItem}>
                  <Text style={styles.paymentName}>{withdrawal.payment_type_name || 'Bilinmiyor'}</Text>
                  <Text style={styles.paymentAmount}>{formatCurrency(withdrawal.total_amount)}</Text>
                </View>
              ))}
              {renderInfoRow('Toplam Para Çıkışı', formatCurrency(sessionData.total_withdrawals))}
              {renderInfoRow('Para Çıkış İşlemi', sessionData.withdrawal_count || 0)}
            </View>
          </>
        )}

        {sessionData.deposit_totals_by_type && sessionData.deposit_totals_by_type.length > 0 && (
          <>
            {renderSectionTitle('Para Giriş İşlemleri')}
            <View style={styles.infoCard}>
              {sessionData.deposit_totals_by_type.map((deposit: any, index: number) => (
                <View key={index} style={styles.paymentItem}>
                  <Text style={styles.paymentName}>{deposit.payment_type_name || 'Bilinmiyor'}</Text>
                  <Text style={styles.paymentAmount}>{formatCurrency(deposit.total_amount)}</Text>
                </View>
              ))}
              {renderInfoRow('Toplam Para Girişi', formatCurrency(sessionData.total_deposits))}
              {renderInfoRow('Para Giriş İşlemi', sessionData.deposit_count || 0)}
            </View>
          </>
        )}

        {/* İşlem Geçmişi */}
        {transactions && transactions.length > 0 && (
          <>
            {renderSectionTitle('İşlem Geçmişi')}

            {/* Filtre butonları */}
            <View style={styles.filterContainer}>
              <TouchableOpacity
                style={[
                  styles.filterButton,
                  transactionFilter === 'all' && styles.activeFilterButton
                ]}
                onPress={() => setTransactionFilter('all')}
              >
                <Text style={[
                  styles.filterButtonText,
                  transactionFilter === 'all' && styles.activeFilterButtonText
                ]}>
                  Tümü
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.filterButton,
                  transactionFilter === 'payment' && styles.activeFilterButton
                ]}
                onPress={() => setTransactionFilter('payment')}
              >
                <Text style={[
                  styles.filterButtonText,
                  transactionFilter === 'payment' && styles.activeFilterButtonText
                ]}>
                  Ödemeler
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.filterButton,
                  transactionFilter === 'withdrawal' && styles.activeFilterButton
                ]}
                onPress={() => setTransactionFilter('withdrawal')}
              >
                <Text style={[
                  styles.filterButtonText,
                  transactionFilter === 'withdrawal' && styles.activeFilterButtonText
                ]}>
                  Para Çıkışları
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.filterButton,
                  transactionFilter === 'deposit' && styles.activeFilterButton
                ]}
                onPress={() => setTransactionFilter('deposit')}
              >
                <Text style={[
                  styles.filterButtonText,
                  transactionFilter === 'deposit' && styles.activeFilterButtonText
                ]}>
                  Para Girişleri
                </Text>
              </TouchableOpacity>
            </View>

            <View style={styles.transactionsContainer}>
              {transactions
                .filter((transaction: any) => {
                  if (transactionFilter === 'all') return true;
                  return transaction.transaction_type === transactionFilter;
                })
                .map((transaction: any, index: number) => (
                <View key={index} style={styles.transactionItem}>
                  <View style={styles.transactionHeader}>
                    <View style={styles.transactionHeaderLeft}>
                      <Icon
                        name={
                          transaction.transaction_type === 'payment' ? 'payments' :
                          transaction.transaction_type === 'withdrawal' ? 'money-off' :
                          transaction.transaction_type === 'deposit' ? 'add-circle' : 'receipt'
                        }
                        size={20}
                        color={
                          transaction.transaction_type === 'payment' ? '#4CAF50' :
                          transaction.transaction_type === 'withdrawal' ? '#FF9800' :
                          transaction.transaction_type === 'deposit' ? '#2196F3' : '#9E9E9E'
                        }
                      />
                      <Text style={styles.transactionType}>
                        {transaction.transaction_type_display ||
                          (transaction.transaction_type === 'payment' ? 'Ödeme' :
                           transaction.transaction_type === 'withdrawal' ? 'Para Çıkışı' :
                           transaction.transaction_type === 'deposit' ? 'Para Girişi' : 'Diğer')
                        }
                      </Text>
                    </View>
                    <Text style={styles.transactionAmount}>
                      {formatCurrency(transaction.amount)}
                    </Text>
                  </View>

                  <View style={styles.transactionDetails}>
                    <View style={styles.transactionDetailRow}>
                      <Text style={styles.transactionDetailLabel}>İşlem Zamanı:</Text>
                      <Text style={styles.transactionDetailValue}>
                        {formatTurkishDateTime(transaction.created_at)}
                      </Text>
                    </View>

                    <View style={styles.transactionDetailRow}>
                      <Text style={styles.transactionDetailLabel}>Ödeme Türü:</Text>
                      <Text style={styles.transactionDetailValue}>
                        {transaction.payment_type || 'Bilinmiyor'}
                      </Text>
                    </View>

                    {transaction.table_title && (
                      <View style={styles.transactionDetailRow}>
                        <Text style={styles.transactionDetailLabel}>Masa:</Text>
                        <Text style={styles.transactionDetailValue}>
                          {transaction.table_title}
                        </Text>
                      </View>
                    )}

                    {transaction.user_name && (
                      <View style={styles.transactionDetailRow}>
                        <Text style={styles.transactionDetailLabel}>Kullanıcı:</Text>
                        <Text style={styles.transactionDetailValue}>
                          {transaction.user_name}
                        </Text>
                      </View>
                    )}

                    {transaction.notes && (
                      <View style={styles.transactionDetailRow}>
                        <Text style={styles.transactionDetailLabel}>Notlar:</Text>
                        <Text style={styles.transactionDetailValue}>
                          {transaction.notes}
                        </Text>
                      </View>
                    )}

                    <View style={styles.transactionDetailRow}>
                      <Text style={styles.transactionDetailLabel}>Durum:</Text>
                      <Text style={[
                        styles.transactionDetailValue,
                        transaction.status === 'completed' ? styles.statusCompleted : styles.statusPending
                      ]}>
                        {transaction.status === 'completed' ? 'Tamamlandı' : 'Beklemede'}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 5,
    marginRight: 15,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'left',
  },
  content: {
    flex: 1,
    padding: 15,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginVertical: 20,
  },
  retryButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
    marginTop: 10,
  },
  infoContainer: {
    backgroundColor: '#fff',
    borderRadius: 8,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  infoItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  infoLabel: {
    fontSize: 16,
    color: '#666',
  },
  infoValue: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  detailCards: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  detailCard: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 8,
    width: '48%',
    marginBottom: 10,
    borderLeftWidth: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  detailCardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  detailCardTitle: {
    marginLeft: 8,
    fontSize: 14,
    color: '#666',
  },
  detailCardValue: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  developmentNote: {
    backgroundColor: '#fff3cd',
    padding: 15,
    borderRadius: 8,
    flexDirection: 'row',
    alignItems: 'center',
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
    marginTop: 20,
  },
  developmentNoteText: {
    marginLeft: 10,
    fontSize: 14,
    color: '#856404',
    flex: 1,
  },
  scrollContainer: {
    flex: 1,
  },
  sessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    padding: 15,
  },
  sessionHeaderInfo: {
    marginLeft: 10,
  },
  sessionName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  sessionStatus: {
    fontSize: 14,
    color: '#666',
    marginTop: 2,
  },
  sectionTitleContainer: {
    backgroundColor: '#f0f0f0',
    paddingVertical: 8,
    paddingHorizontal: 15,
  },
  infoCard: {
    backgroundColor: '#fff',
    padding: 15,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  paymentItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  paymentName: {
    fontSize: 14,
    color: '#333',
  },
  paymentAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  transactionsContainer: {
    marginBottom: 20,
  },
  transactionItem: {
    backgroundColor: '#fff',
    marginBottom: 10,
    borderRadius: 5,
    overflow: 'hidden',
  },
  transactionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 12,
    backgroundColor: '#f5f5f5',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  transactionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  transactionType: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  transactionAmount: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  transactionDetails: {
    padding: 12,
  },
  transactionDetailRow: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  transactionDetailLabel: {
    fontSize: 13,
    color: '#666',
    width: 100,
  },
  transactionDetailValue: {
    fontSize: 13,
    color: '#333',
    flex: 1,
  },
  statusCompleted: {
    color: '#4CAF50',
    fontWeight: '500',
  },
  statusPending: {
    color: '#FF9800',
    fontWeight: '500',
  },
  filterContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    backgroundColor: '#fff',
    padding: 10,
    marginBottom: 10,
    justifyContent: 'space-between',
  },
  filterButton: {
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    marginBottom: 5,
    minWidth: '22%',
    alignItems: 'center',
  },
  activeFilterButton: {
    backgroundColor: '#4CAF50',
  },
  filterButtonText: {
    fontSize: 12,
    color: '#333',
    textAlign: 'center',
  },
  activeFilterButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
});

export default CashRegisterDetailScreen;
