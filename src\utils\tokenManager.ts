import AsyncStorage from '@react-native-async-storage/async-storage';
import { refreshToken } from '../config/api';

const TOKEN_KEY = 'userToken';
const REFRESH_TOKEN_KEY = 'refreshToken';
const TOKEN_EXPIRY_KEY = 'tokenExpiry';

interface TokenResponse {
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export const setTokens = async (
  accessToken: string,
  refreshTokenValue: string,
  expiresIn: number
): Promise<void> => {
  const expiryDate = new Date(new Date().getTime() + expiresIn * 1000);
  await AsyncStorage.setItem(TOKEN_KEY, accessToken);
  await AsyncStorage.setItem(REFRESH_TOKEN_KEY, refreshTokenValue);
  await AsyncStorage.setItem(TOKEN_EXPIRY_KEY, expiryDate.toISOString());
};

export const getAccessToken = async (): Promise<string | null> => {
  const token = await AsyncStorage.getItem(TOKEN_KEY);
  const expiryDate = await AsyncStorage.getItem(TOKEN_EXPIRY_KEY);

  if (!token || !expiryDate) {
    return null;
  }

  if (new Date(expiryDate) <= new Date()) {
    return await refreshAccessToken();
  }

  return token;
};

export const refreshAccessToken = async (): Promise<string | null> => {
  try {
    const currentRefreshToken = await AsyncStorage.getItem(REFRESH_TOKEN_KEY);
    if (!currentRefreshToken) {
      return null;
    }

    const response: TokenResponse = await refreshToken(currentRefreshToken);
    await setTokens(response.accessToken, response.refreshToken, response.expiresIn);
    return response.accessToken;
  } catch (error) {
    console.error('Token yenileme hatası:', error);
    await clearTokens();
    return null;
  }
};

export const clearTokens = async (): Promise<void> => {
  await AsyncStorage.removeItem(TOKEN_KEY);
  await AsyncStorage.removeItem(REFRESH_TOKEN_KEY);
  await AsyncStorage.removeItem(TOKEN_EXPIRY_KEY);
};