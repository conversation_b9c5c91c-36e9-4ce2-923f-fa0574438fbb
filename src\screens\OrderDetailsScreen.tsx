import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
  TextInput,
  FlatList,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { getOrderDetails } from '../config/api';
import { formatCurrency, formatTurkishDateTime } from '../utils/dateUtils';

type NavigationProp = StackNavigationProp<RootStackParamList, 'OrderDetails'>;
type RoutePropType = RouteProp<RootStackParamList, 'OrderDetails'>;

const OrderDetailsScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RoutePropType>();
  const [orderDetails, setOrderDetails] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [orderFilters, setOrderFilters] = useState<{
    status: string;
    paymentStatus: string;
    searchText: string;
    table: string;
    user: string;
    itemStatus: string;
    minAmount: string;
    maxAmount: string;
  }>({
    status: 'all',
    paymentStatus: 'all',
    searchText: '',
    table: 'all',
    user: 'all',
    itemStatus: 'all',
    minAmount: '',
    maxAmount: ''
  });
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async (): Promise<void> => {
    try {
      setLoading(true);
      const { reportType, startDate, endDate } = route.params;

      let filters: any = { type: reportType };
      if (reportType === 'custom' && startDate && endDate) {
        filters.startDate = startDate;
        filters.endDate = endDate;
      }

      const response = await getOrderDetails(filters);

      setOrderDetails(response);
    } catch (error) {
      console.error('Error fetching order details:', error);
      setOrderDetails(null);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async (): Promise<void> => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const handleFilterChange = (filterType: keyof typeof orderFilters, value: string): void => {
    setOrderFilters(prev => ({
      ...prev,
      [filterType]: value
    }));
  };

  const getFilteredOrders = (): any[] => {
    if (!orderDetails || !orderDetails.data || !orderDetails.data.orders) {
      return [];
    }

    return orderDetails.data.orders.filter((order: any) => {
      // Durum filtresi
      if (orderFilters.status !== 'all' && order.status !== orderFilters.status) {
        return false;
      }

      // Ödeme durumu filtresi
      if (orderFilters.paymentStatus !== 'all' && order.payment_status !== orderFilters.paymentStatus) {
        return false;
      }

      // Masa filtresi
      if (orderFilters.table !== 'all' && order.table_title !== orderFilters.table) {
        return false;
      }

      // Kullanıcı filtresi
      if (orderFilters.user !== 'all' && order.user_name !== orderFilters.user) {
        return false;
      }

      // Tutar filtresi
      if (orderFilters.minAmount && parseFloat(order.calculatedTotal) < parseFloat(orderFilters.minAmount)) {
        return false;
      }

      if (orderFilters.maxAmount && parseFloat(order.calculatedTotal) > parseFloat(orderFilters.maxAmount)) {
        return false;
      }

      // Ürün durumu filtresi (orderItems'dan kontrol)
      if (orderFilters.itemStatus !== 'all') {
        const orderItems = orderDetails.data.orderItems.filter((item: any) => item.order_id === order.id);
        const hasItemWithStatus = orderItems.some((item: any) => item.status === orderFilters.itemStatus);
        if (!hasItemWithStatus) {
          return false;
        }
      }

      // Arama filtresi
      if (orderFilters.searchText) {
        const searchLower = orderFilters.searchText.toLowerCase();
        const tableMatch = order.table_title && order.table_title.toLowerCase().includes(searchLower);
        const idMatch = order.id.toString().includes(searchLower);
        const customerMatch = order.customer_name && order.customer_name.toLowerCase().includes(searchLower);
        const userMatch = order.user_name && order.user_name.toLowerCase().includes(searchLower);

        if (!tableMatch && !idMatch && !customerMatch && !userMatch) {
          return false;
        }
      }

      return true;
    });
  };

  const getStatusColor = (status: string): string => {
    switch (status) {
      case 'completed': return '#4CAF50';
      case 'pending': return '#FF9800';
      case 'cancelled': return '#F44336';
      default: return '#666';
    }
  };

  const getStatusText = (status: string): string => {
    switch (status) {
      case 'completed': return 'Tamamlandı';
      case 'pending': return 'Beklemede';
      case 'cancelled': return 'İptal';
      case 'created': return 'Oluşturuldu';
      default: return status;
    }
  };

  const getPaymentStatusText = (status: string): string => {
    switch (status) {
      case 'paid': return 'Ödendi';
      case 'pending': return 'Beklemede';
      case 'cancelled': return 'İptal';
      case 'partial': return 'Kısmi';
      default: return status;
    }
  };

  // Unique değerler için helper fonksiyonlar
  const getUniqueTables = (): string[] => {
    if (!orderDetails?.data?.orders) return [];
    const tables = orderDetails.data.orders.map((order: any) => order.table_title).filter(Boolean);
    return [...new Set(tables)] as string[];
  };

  const getUniqueUsers = (): string[] => {
    if (!orderDetails?.data?.orders) return [];
    const users = orderDetails.data.orders.map((order: any) => order.user_name).filter(Boolean);
    return [...new Set(users)] as string[];
  };

  const getUniqueItemStatuses = (): string[] => {
    if (!orderDetails?.data?.orderItems) return [];
    const statuses = orderDetails.data.orderItems.map((item: any) => item.status).filter(Boolean);
    return [...new Set(statuses)] as string[];
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Sipariş Detayları</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  const filteredOrders = getFilteredOrders();

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Sipariş Detayları</Text>
      </View>

      {/* Filtreler */}
      <View style={styles.filterContainer}>
        {/* Temel Filtreler */}
        <View style={styles.filterRow}>
          <Text style={styles.filterLabel}>Durum:</Text>
          <TouchableOpacity
            style={[styles.filterChip, orderFilters.status === 'all' && styles.activeFilterChip]}
            onPress={() => handleFilterChange('status', 'all')}
          >
            <Text style={[styles.filterChipText, orderFilters.status === 'all' && styles.activeFilterChipText]}>
              Tümü
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterChip, orderFilters.status === 'completed' && styles.activeFilterChip]}
            onPress={() => handleFilterChange('status', 'completed')}
          >
            <Text style={[styles.filterChipText, orderFilters.status === 'completed' && styles.activeFilterChipText]}>
              Tamamlanan
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterChip, orderFilters.status === 'created' && styles.activeFilterChip]}
            onPress={() => handleFilterChange('status', 'created')}
          >
            <Text style={[styles.filterChipText, orderFilters.status === 'created' && styles.activeFilterChipText]}>
              Oluşturuldu
            </Text>
          </TouchableOpacity>
        </View>

        {/* Ödeme Durumu */}
        <View style={styles.filterRow}>
          <Text style={styles.filterLabel}>Ödeme:</Text>
          <TouchableOpacity
            style={[styles.filterChip, orderFilters.paymentStatus === 'all' && styles.activeFilterChip]}
            onPress={() => handleFilterChange('paymentStatus', 'all')}
          >
            <Text style={[styles.filterChipText, orderFilters.paymentStatus === 'all' && styles.activeFilterChipText]}>
              Tümü
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterChip, orderFilters.paymentStatus === 'paid' && styles.activeFilterChip]}
            onPress={() => handleFilterChange('paymentStatus', 'paid')}
          >
            <Text style={[styles.filterChipText, orderFilters.paymentStatus === 'paid' && styles.activeFilterChipText]}>
              Ödendi
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterChip, orderFilters.paymentStatus === 'partial' && styles.activeFilterChip]}
            onPress={() => handleFilterChange('paymentStatus', 'partial')}
          >
            <Text style={[styles.filterChipText, orderFilters.paymentStatus === 'partial' && styles.activeFilterChipText]}>
              Kısmi
            </Text>
          </TouchableOpacity>
          <TouchableOpacity
            style={[styles.filterChip, orderFilters.paymentStatus === 'pending' && styles.activeFilterChip]}
            onPress={() => handleFilterChange('paymentStatus', 'pending')}
          >
            <Text style={[styles.filterChipText, orderFilters.paymentStatus === 'pending' && styles.activeFilterChipText]}>
              Beklemede
            </Text>
          </TouchableOpacity>
        </View>

        {/* Gelişmiş Filtreler Toggle */}
        <TouchableOpacity
          style={styles.advancedToggle}
          onPress={() => setShowAdvancedFilters(!showAdvancedFilters)}
        >
          <Text style={styles.advancedToggleText}>
            Gelişmiş Filtreler
          </Text>
          <Icon
            name={showAdvancedFilters ? "expand-less" : "expand-more"}
            size={20}
            color="#2196F3"
          />
        </TouchableOpacity>

        {/* Gelişmiş Filtreler */}
        {showAdvancedFilters && (
          <View style={styles.advancedFilters}>
            {/* Masa Filtresi */}
            <View style={styles.filterRow}>
              <Text style={styles.filterLabel}>Masa:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <TouchableOpacity
                  style={[styles.filterChip, orderFilters.table === 'all' && styles.activeFilterChip]}
                  onPress={() => handleFilterChange('table', 'all')}
                >
                  <Text style={[styles.filterChipText, orderFilters.table === 'all' && styles.activeFilterChipText]}>
                    Tümü
                  </Text>
                </TouchableOpacity>
                {getUniqueTables().map((table) => (
                  <TouchableOpacity
                    key={table}
                    style={[styles.filterChip, orderFilters.table === table && styles.activeFilterChip]}
                    onPress={() => handleFilterChange('table', table)}
                  >
                    <Text style={[styles.filterChipText, orderFilters.table === table && styles.activeFilterChipText]}>
                      {table}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Kullanıcı Filtresi */}
            <View style={styles.filterRow}>
              <Text style={styles.filterLabel}>Kullanıcı:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <TouchableOpacity
                  style={[styles.filterChip, orderFilters.user === 'all' && styles.activeFilterChip]}
                  onPress={() => handleFilterChange('user', 'all')}
                >
                  <Text style={[styles.filterChipText, orderFilters.user === 'all' && styles.activeFilterChipText]}>
                    Tümü
                  </Text>
                </TouchableOpacity>
                {getUniqueUsers().map((user) => (
                  <TouchableOpacity
                    key={user}
                    style={[styles.filterChip, orderFilters.user === user && styles.activeFilterChip]}
                    onPress={() => handleFilterChange('user', user)}
                  >
                    <Text style={[styles.filterChipText, orderFilters.user === user && styles.activeFilterChipText]}>
                      {user}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Ürün Durumu Filtresi */}
            <View style={styles.filterRow}>
              <Text style={styles.filterLabel}>Ürün Durumu:</Text>
              <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                <TouchableOpacity
                  style={[styles.filterChip, orderFilters.itemStatus === 'all' && styles.activeFilterChip]}
                  onPress={() => handleFilterChange('itemStatus', 'all')}
                >
                  <Text style={[styles.filterChipText, orderFilters.itemStatus === 'all' && styles.activeFilterChipText]}>
                    Tümü
                  </Text>
                </TouchableOpacity>
                {getUniqueItemStatuses().map((status) => (
                  <TouchableOpacity
                    key={status}
                    style={[styles.filterChip, orderFilters.itemStatus === status && styles.activeFilterChip]}
                    onPress={() => handleFilterChange('itemStatus', status)}
                  >
                    <Text style={[styles.filterChipText, orderFilters.itemStatus === status && styles.activeFilterChipText]}>
                      {status === 'created' ? 'Oluşturuldu' :
                       status === 'cancelled' ? 'İptal' :
                       status === 'complimentary' ? 'İkram' :
                       status === 'waste' ? 'Zayi' : status}
                    </Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>

            {/* Tutar Filtresi */}
            <View style={styles.amountFilterRow}>
              <Text style={styles.filterLabel}>Tutar:</Text>
              <TextInput
                style={styles.amountInput}
                placeholder="Min"
                value={orderFilters.minAmount}
                onChangeText={(text) => handleFilterChange('minAmount', text)}
                keyboardType="numeric"
              />
              <Text style={styles.amountSeparator}>-</Text>
              <TextInput
                style={styles.amountInput}
                placeholder="Max"
                value={orderFilters.maxAmount}
                onChangeText={(text) => handleFilterChange('maxAmount', text)}
                keyboardType="numeric"
              />
            </View>
          </View>
        )}

        <TextInput
          style={styles.searchInput}
          placeholder="Masa, sipariş ID, müşteri adı veya kullanıcı ara..."
          value={orderFilters.searchText}
          onChangeText={(text) => handleFilterChange('searchText', text)}
        />
      </View>

      <View style={styles.summaryContainer}>
        <Text style={styles.summaryText}>
          {filteredOrders.length} sipariş bulundu
        </Text>
      </View>

      <FlatList
        data={filteredOrders}
        keyExtractor={(item) => item.id.toString()}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.orderItem}
            onPress={() => {
              const orderItems = orderDetails.data.orderItems.filter((orderItem: any) => orderItem.order_id === item.id);
              const payments = orderDetails.data.payments.filter((payment: any) => payment.order_id === item.id);
              navigation.navigate('SingleOrderDetail', {
                order: item,
                orderItems,
                payments
              });
            }}
          >
            <View style={styles.orderHeader}>
              <Text style={styles.orderNumber}>#{item.id}</Text>
              <Text style={styles.orderTotal}>{formatCurrency(item.calculatedTotal)}</Text>
            </View>

            <View style={styles.orderInfo}>
              <Text style={styles.orderTable}>{item.table_title}</Text>
              <Text style={styles.orderTime}>{formatTurkishDateTime(item.date)}</Text>
            </View>

            <View style={styles.orderInfo}>
              <Text style={styles.orderUser}>{item.user_name}</Text>
              <Text style={styles.orderToken}>Token: {item.token_no}</Text>
            </View>

            {item.customer_name && (
              <Text style={styles.customerName}>Müşteri: {item.customer_name}</Text>
            )}

            {/* Sipariş Detayları */}
            <View style={styles.orderDetails}>
              <Text style={styles.orderDetailText}>
                Net: {formatCurrency(item.netTotal)} | Brüt: {formatCurrency(item.grossTotal)}
              </Text>
              {(item.cancelledTotal > 0 || item.wasteTotal > 0 || item.complimentaryTotal > 0 || item.discountTotal > 0) && (
                <Text style={styles.orderDetailText}>
                  {item.discountTotal > 0 && `İndirim: ${formatCurrency(item.discountTotal)} `}
                  {item.cancelledTotal > 0 && `İptal: ${formatCurrency(item.cancelledTotal)} `}
                  {item.wasteTotal > 0 && `Zayi: ${formatCurrency(item.wasteTotal)} `}
                  {item.complimentaryTotal > 0 && `İkram: ${formatCurrency(item.complimentaryTotal)}`}
                </Text>
              )}
            </View>

            <View style={styles.statusContainer}>
              <View style={styles.statusBadge}>
                <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
                  {getStatusText(item.status)}
                </Text>
              </View>
              <View style={styles.statusBadge}>
                <Text style={[styles.statusText, { color: getStatusColor(item.payment_status) }]}>
                  {getPaymentStatusText(item.payment_status)}
                </Text>
              </View>
              {item.calculatedRemaining > 0 && (
                <View style={[styles.statusBadge, { backgroundColor: '#FF9800' }]}>
                  <Text style={[styles.statusText, { color: '#fff' }]}>
                    Kalan: {formatCurrency(item.calculatedRemaining)}
                  </Text>
                </View>
              )}
            </View>
          </TouchableOpacity>
        )}
        ListEmptyComponent={() => (
          <View style={styles.emptyContainer}>
            <Icon name="inbox" size={60} color="#ccc" />
            <Text style={styles.emptyText}>Sipariş bulunamadı</Text>
          </View>
        )}
        contentContainerStyle={filteredOrders.length === 0 ? styles.emptyList : undefined}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingTop: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  filterContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  filterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  filterLabel: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginRight: 10,
  },
  filterChip: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: '#f0f0f0',
    marginRight: 8,
  },
  activeFilterChip: {
    backgroundColor: '#2196F3',
  },
  filterChipText: {
    fontSize: 12,
    color: '#666',
  },
  activeFilterChipText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  searchInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    backgroundColor: '#f9f9f9',
  },
  summaryContainer: {
    backgroundColor: '#fff',
    padding: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  summaryText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  orderItem: {
    backgroundColor: '#fff',
    padding: 15,
    marginHorizontal: 10,
    marginVertical: 5,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  orderInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  orderTable: {
    fontSize: 14,
    color: '#666',
  },
  orderTime: {
    fontSize: 12,
    color: '#999',
  },
  customerName: {
    fontSize: 14,
    color: '#333',
    marginBottom: 8,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: '#f0f0f0',
  },
  statusText: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 10,
    textAlign: 'center',
  },
  emptyList: {
    flexGrow: 1,
  },
  advancedToggle: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 5,
    marginVertical: 5,
  },
  advancedToggleText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#2196F3',
  },
  advancedFilters: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  amountFilterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  amountInput: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 6,
    paddingHorizontal: 8,
    paddingVertical: 6,
    fontSize: 14,
    backgroundColor: '#f9f9f9',
    width: 80,
    marginHorizontal: 5,
  },
  amountSeparator: {
    fontSize: 16,
    color: '#666',
    marginHorizontal: 5,
  },
  orderUser: {
    fontSize: 14,
    color: '#666',
  },
  orderToken: {
    fontSize: 12,
    color: '#999',
  },
  orderDetails: {
    marginVertical: 8,
    paddingVertical: 8,
    paddingHorizontal: 10,
    backgroundColor: '#f9f9f9',
    borderRadius: 6,
  },
  orderDetailText: {
    fontSize: 12,
    color: '#666',
    lineHeight: 18,
  },
});

export default OrderDetailsScreen;
