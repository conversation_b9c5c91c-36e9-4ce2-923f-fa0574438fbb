import React from 'react';
import { View, Text, TouchableOpacity, StyleSheet, Modal } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { ModalProps } from '../types';

type AdminOption = 'sales' | 'reports' | 'inventory';

interface AdminSelectionModalProps extends ModalProps {
  onSelectOption: (option: AdminOption) => void;
}

const AdminSelectionModal: React.FC<AdminSelectionModalProps> = ({ 
  isVisible, 
  onClose, 
  onSelectOption 
}) => {
  const options = [
    {
      key: 'sales' as AdminOption,
      icon: 'point-of-sale',
      color: '#4CAF50',
      text: 'Satış Ekranı'
    },
    {
      key: 'reports' as AdminOption,
      icon: 'assessment',
      color: '#2196F3',
      text: 'Rapor Ekranı'
    },
    {
      key: 'inventory' as AdminOption,
      icon: 'inventory',
      color: '#FF9800',
      text: 'Stok Raporu'
    }
  ];

  return (
    <Modal
      animationType="fade"
      transparent={true}
      visible={isVisible}
      onRequestClose={onClose}
    >
      <View style={styles.modalContainer}>
        <View style={styles.modalContent}>
          <Text style={styles.modalTitle}>Hoş Geldiniz, Admin</Text>
          <Text style={styles.modalSubtitle}>Lütfen bir seçenek belirleyin</Text>
          
          {options.map((option) => (
            <TouchableOpacity 
              key={option.key}
              style={styles.optionButton}
              onPress={() => onSelectOption(option.key)}
              activeOpacity={0.7}
            >
              <Icon name={option.icon} size={24} color={option.color} />
              <Text style={styles.optionText}>{option.text}</Text>
            </TouchableOpacity>
          ))}
          
          <TouchableOpacity 
            style={styles.closeButton}
            onPress={onClose}
            activeOpacity={0.7}
          >
            <Text style={styles.closeButtonText}>İptal</Text>
          </TouchableOpacity>
        </View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 15,
    padding: 20,
    width: '80%',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  modalTitle: {
    fontSize: 22,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    textAlign: 'center',
  },
  modalSubtitle: {
    fontSize: 16,
    color: '#666',
    marginBottom: 20,
    textAlign: 'center',
  },
  optionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    padding: 15,
    borderRadius: 10,
    marginVertical: 8,
    width: '100%',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  optionText: {
    fontSize: 16,
    marginLeft: 15,
    color: '#333',
    fontWeight: '500',
  },
  closeButton: {
    marginTop: 15,
    paddingVertical: 10,
    paddingHorizontal: 20,
  },
  closeButtonText: {
    fontSize: 16,
    color: '#666',
    fontWeight: '500',
  },
});

export default AdminSelectionModal;
