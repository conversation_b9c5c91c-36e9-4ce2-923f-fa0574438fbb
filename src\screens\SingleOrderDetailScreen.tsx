import React from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  StyleSheet,
  SafeAreaView,
  FlatList
} from 'react-native';
import { useRoute, useNavigation, RouteProp } from '@react-navigation/native';
import { NavigationProp } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { formatCurrency, formatTurkishDateTime } from '../utils/dateUtils';

type RouteParams = {
  order: any;
  orderItems: any[];
  payments: any[];
};

type RoutePropType = RouteProp<{ params: RouteParams }, 'params'>;

const SingleOrderDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp<any>>();
  const route = useRoute<RoutePropType>();
  const { order, orderItems, payments } = route.params;

  const getItemStatusColor = (status: string): string => {
    switch (status) {
      case 'created': return '#2196F3';
      case 'cancelled': return '#F44336';
      case 'complimentary': return '#4CAF50';
      case 'waste': return '#FF9800';
      default: return '#666';
    }
  };

  const getItemStatusText = (status: string): string => {
    switch (status) {
      case 'created': return 'Normal';
      case 'cancelled': return 'İptal';
      case 'complimentary': return 'İkram';
      case 'waste': return 'Zayi';
      default: return status;
    }
  };

  const renderOrderItem = ({ item }: { item: any }) => (
    <View style={styles.itemContainer}>
      <View style={styles.itemHeader}>
        <Text style={styles.itemTitle}>{item.item_title}</Text>
        <View style={styles.itemPriceContainer}>
          <Text style={styles.itemQuantity}>x{item.quantity}</Text>
          <Text style={styles.itemPrice}>
            {formatCurrency(parseFloat(item.order_item_price) * item.quantity)}
          </Text>
        </View>
      </View>
      
      {item.variant_title && (
        <Text style={styles.itemVariant}>Varyant: {item.variant_title}</Text>
      )}
      
      {item.notes && (
        <Text style={styles.itemNotes}>Not: {item.notes}</Text>
      )}

      {item.addons && item.addons !== 'null' && (
        <Text style={styles.itemAddons}>Ekstralar mevcut</Text>
      )}
      
      <View style={styles.itemFooter}>
        <View style={[styles.statusBadge, { backgroundColor: getItemStatusColor(item.status) }]}>
          <Text style={styles.statusText}>{getItemStatusText(item.status)}</Text>
        </View>
        <Text style={styles.itemTime}>{formatTurkishDateTime(item.date)}</Text>
      </View>

      {/* İptal/İkram/Fire bilgileri */}
      {item.status !== 'created' && (
        <View style={styles.actionInfo}>
          {item.cancelled_by_name && (
            <Text style={styles.actionText}>İptal eden: {item.cancelled_by_name}</Text>
          )}
          {item.complimentary_by_name && (
            <Text style={styles.actionText}>İkram eden: {item.complimentary_by_name}</Text>
          )}
          {item.waste_by_name && (
            <Text style={styles.actionText}>Zayi eden: {item.waste_by_name}</Text>
          )}
          {(item.cancelled_reason_title || item.complimentary_reason_title || item.waste_reason_title) && (
            <Text style={styles.actionReason}>
              Sebep: {item.cancelled_reason_title || item.complimentary_reason_title || item.waste_reason_title}
            </Text>
          )}
        </View>
      )}
    </View>
  );

  const renderPayment = ({ item }: { item: any }) => (
    <View style={styles.paymentContainer}>
      <View style={styles.paymentHeader}>
        <Text style={styles.paymentType}>{item.payment_type}</Text>
        <Text style={styles.paymentAmount}>{formatCurrency(item.amount)}</Text>
      </View>
      <Text style={styles.paymentTime}>{formatTurkishDateTime(item.created_at)}</Text>
      <Text style={styles.paymentUser}>İşlem yapan: {item.user_name}</Text>
    </View>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Sipariş #{order.id}</Text>
      </View>

      <ScrollView style={styles.content}>
        {/* Sipariş Özeti */}
        <View style={styles.summaryContainer}>
          <Text style={styles.sectionTitle}>Sipariş Bilgileri</Text>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Masa:</Text>
            <Text style={styles.summaryValue}>{order.table_title}</Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Tarih:</Text>
            <Text style={styles.summaryValue}>{formatTurkishDateTime(order.date)}</Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Sipariş Veren:</Text>
            <Text style={styles.summaryValue}>{order.user_name}</Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Token No:</Text>
            <Text style={styles.summaryValue}>{order.token_no}</Text>
          </View>

          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Durum:</Text>
            <Text style={[styles.summaryValue, { color: getItemStatusColor(order.status) }]}>
              {getItemStatusText(order.status)}
            </Text>
          </View>
        </View>

        {/* Finansal Özet */}
        <View style={styles.summaryContainer}>
          <Text style={styles.sectionTitle}>Finansal Özet</Text>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Brüt Toplam:</Text>
            <Text style={styles.summaryValue}>{formatCurrency(order.grossTotal)}</Text>
          </View>
          
          {order.discountTotal > 0 && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>İndirim:</Text>
              <Text style={[styles.summaryValue, { color: '#FF9800' }]}>
                -{formatCurrency(order.discountTotal)}
              </Text>
            </View>
          )}
          
          {order.cancelledTotal > 0 && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>İptal Edilen:</Text>
              <Text style={[styles.summaryValue, { color: '#F44336' }]}>
                -{formatCurrency(order.cancelledTotal)}
              </Text>
            </View>
          )}
          
          {order.wasteTotal > 0 && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Zayi:</Text>
              <Text style={[styles.summaryValue, { color: '#FF9800' }]}>
                -{formatCurrency(order.wasteTotal)}
              </Text>
            </View>
          )}
          
          {order.complimentaryTotal > 0 && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>İkram:</Text>
              <Text style={[styles.summaryValue, { color: '#4CAF50' }]}>
                -{formatCurrency(order.complimentaryTotal)}
              </Text>
            </View>
          )}
          
          <View style={[styles.summaryRow, styles.totalRow]}>
            <Text style={styles.totalLabel}>Net Toplam:</Text>
            <Text style={styles.totalValue}>{formatCurrency(order.netTotal)}</Text>
          </View>
          
          <View style={styles.summaryRow}>
            <Text style={styles.summaryLabel}>Ödenen:</Text>
            <Text style={[styles.summaryValue, { color: '#4CAF50' }]}>
              {formatCurrency(order.calculatedPaid)}
            </Text>
          </View>
          
          {order.calculatedRemaining > 0 && (
            <View style={styles.summaryRow}>
              <Text style={styles.summaryLabel}>Kalan:</Text>
              <Text style={[styles.summaryValue, { color: '#F44336' }]}>
                {formatCurrency(order.calculatedRemaining)}
              </Text>
            </View>
          )}
        </View>

        {/* Ürünler */}
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Ürünler ({orderItems.length})</Text>
          <FlatList
            data={orderItems}
            renderItem={renderOrderItem}
            keyExtractor={item => item.id.toString()}
            scrollEnabled={false}
          />
        </View>

        {/* Ödemeler */}
        {payments.length > 0 && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Ödemeler ({payments.length})</Text>
            <FlatList
              data={payments}
              renderItem={renderPayment}
              keyExtractor={item => item.id.toString()}
              scrollEnabled={false}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  content: {
    flex: 1,
  },
  section: {
    backgroundColor: '#fff',
    margin: 10,
    borderRadius: 8,
    padding: 15,
  },
  summaryContainer: {
    backgroundColor: '#fff',
    margin: 10,
    borderRadius: 8,
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
  },
  summaryLabel: {
    fontSize: 14,
    color: '#666',
  },
  summaryValue: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333',
  },
  totalRow: {
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
    marginTop: 10,
    paddingTop: 15,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  totalValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  itemContainer: {
    backgroundColor: '#f9f9f9',
    padding: 12,
    marginVertical: 4,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#2196F3',
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 4,
  },
  itemTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    flex: 1,
  },
  itemPriceContainer: {
    alignItems: 'flex-end',
  },
  itemQuantity: {
    fontSize: 14,
    color: '#666',
    marginBottom: 2,
  },
  itemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  itemVariant: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 4,
  },
  itemNotes: {
    fontSize: 14,
    color: '#FF9800',
    marginBottom: 4,
  },
  itemAddons: {
    fontSize: 12,
    color: '#2196F3',
    marginBottom: 4,
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    color: '#fff',
    fontSize: 12,
    fontWeight: '600',
  },
  itemTime: {
    fontSize: 12,
    color: '#999',
  },
  actionInfo: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  actionText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  actionReason: {
    fontSize: 12,
    color: '#999',
    fontStyle: 'italic',
  },
  paymentContainer: {
    backgroundColor: '#f9f9f9',
    padding: 12,
    marginVertical: 4,
    borderRadius: 8,
    borderLeftWidth: 3,
    borderLeftColor: '#4CAF50',
  },
  paymentHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  paymentType: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  paymentAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  paymentTime: {
    fontSize: 12,
    color: '#999',
    marginBottom: 2,
  },
  paymentUser: {
    fontSize: 12,
    color: '#666',
  },
});

export default SingleOrderDetailScreen;
