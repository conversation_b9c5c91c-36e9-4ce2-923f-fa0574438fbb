import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  Switch,
  Modal,
  TextInput
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { logout } from '../config/api';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';

type SettingsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Settings'>;

interface SettingsScreenProps {
  navigation?: SettingsScreenNavigationProp;
}

interface SettingItem {
  id: string;
  title: string;
  subtitle?: string;
  icon: string;
  onPress?: () => void;
  color?: string;
  type?: 'action' | 'toggle' | 'input';
  value?: boolean | string;
  onToggle?: (value: boolean) => void;
}

const SettingsScreen: React.FC<SettingsScreenProps> = ({ navigation }) => {
  const [notifications, setNotifications] = useState<boolean>(true);
  const [darkMode, setDarkMode] = useState<boolean>(false);
  const [autoBackup, setAutoBackup] = useState<boolean>(true);
  const [profileModalVisible, setProfileModalVisible] = useState<boolean>(false);
  const [userName, setUserName] = useState<string>('Admin User');
  const [userEmail, setUserEmail] = useState<string>('<EMAIL>');

  const handleLogout = async (): Promise<void> => {
    Alert.alert(
      'Çıkış Yap',
      'Çıkış yapmak istediğinizden emin misiniz?',
      [
        {
          text: 'İptal',
          style: 'cancel',
        },
        {
          text: 'Çıkış Yap',
          style: 'destructive',
          onPress: async () => {
            try {
              await logout();
              navigation?.reset({
                index: 0,
                routes: [{ name: 'Login' }],
              });
            } catch (error) {
              console.error('Logout error:', error);
              Alert.alert('Hata', 'Çıkış yapılırken bir hata oluştu');
            }
          },
        },
      ]
    );
  };

  const settingsItems: SettingItem[] = [
    {
      id: 'profile',
      title: 'Profil Ayarları',
      subtitle: 'Kullanıcı bilgilerini düzenle',
      icon: 'person',
      type: 'action',
      onPress: () => setProfileModalVisible(true),
    },
    {
      id: 'notifications',
      title: 'Bildirimler',
      subtitle: 'Push bildirimleri al',
      icon: 'notifications',
      type: 'toggle',
      value: notifications,
      onToggle: setNotifications,
    },
    {
      id: 'darkMode',
      title: 'Karanlık Mod',
      subtitle: 'Koyu tema kullan',
      icon: 'dark-mode',
      type: 'toggle',
      value: darkMode,
      onToggle: setDarkMode,
    },
    {
      id: 'autoBackup',
      title: 'Otomatik Yedekleme',
      subtitle: 'Verileri otomatik yedekle',
      icon: 'backup',
      type: 'toggle',
      value: autoBackup,
      onToggle: setAutoBackup,
    },
    {
      id: 'security',
      title: 'Güvenlik',
      subtitle: 'Şifre ve güvenlik ayarları',
      icon: 'security',
      type: 'action',
      onPress: () => {
        Alert.alert('Güvenlik', 'Güvenlik ayarları yakında eklenecek');
      },
    },
    {
      id: 'language',
      title: 'Dil Ayarları',
      subtitle: 'Uygulama dilini değiştir',
      icon: 'language',
      type: 'action',
      onPress: () => {
        Alert.alert('Dil', 'Dil ayarları yakında eklenecek');
      },
    },
    {
      id: 'about',
      title: 'Hakkında',
      subtitle: 'Uygulama bilgileri',
      icon: 'info',
      type: 'action',
      onPress: () => {
        Alert.alert('SewPOS', 'Versiyon 1.0.0\n\nGeliştirici: SewPOS Team\n\nTelif Hakkı © 2024');
      },
    },
    {
      id: 'logout',
      title: 'Çıkış Yap',
      subtitle: 'Hesaptan çıkış yap',
      icon: 'logout',
      type: 'action',
      onPress: handleLogout,
      color: '#F44336',
    },
  ];

  const renderSettingItem = (item: SettingItem) => (
    <TouchableOpacity
      key={item.id}
      style={styles.settingItem}
      onPress={item.onPress}
      activeOpacity={0.7}
    >
      <View style={styles.settingItemLeft}>
        <View style={[styles.iconContainer, item.color ? { backgroundColor: `${item.color}20` } : undefined]}>
          <Icon
            name={item.icon}
            size={24}
            color={item.color || '#4CAF50'}
          />
        </View>
        <View style={styles.textContainer}>
          <Text style={[styles.settingTitle, item.color ? { color: item.color } : undefined]}>
            {item.title}
          </Text>
          {item.subtitle && (
            <Text style={styles.settingSubtitle}>{item.subtitle}</Text>
          )}
        </View>
      </View>
      <Icon name="chevron-right" size={24} color="#ccc" />
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Ayarlar</Text>
      </View>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.section}>
          {settingsItems.map(renderSettingItem)}
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#4CAF50',
    paddingVertical: 20,
    paddingHorizontal: 20,
    paddingTop: 40,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    padding: 20,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  settingItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: '#4CAF5020',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  settingSubtitle: {
    fontSize: 14,
    color: '#666',
  },
});

export default SettingsScreen;
