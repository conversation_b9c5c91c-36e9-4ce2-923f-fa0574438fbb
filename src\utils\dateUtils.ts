/**
 * Tarih ve saat işlemleri için yardımcı fonksiyonlar
 */

/**
 * Verilen tarihi Türkiye saatine (UTC+3) göre formatlar
 * @param date - Formatlanacak tarih
 * @param includeTime - Saat bilgisinin dahil edilip edilmeyeceği
 * @returns Formatlanmış tarih
 */
export const formatTurkishDateTime = (date: string | Date | null | undefined, includeTime: boolean = true): string => {
  if (!date) return '-';

  try {
    // Tarih nesnesine çevir - UTC olarak parse et
    let dateObj: Date;
    if (typeof date === 'string') {
      // Eğer string UTC formatında değilse, UTC olarak parse et
      if (!date.includes('Z') && !date.includes('+')) {
        dateObj = new Date(date + 'Z'); // UTC olarak parse et
      } else {
        dateObj = new Date(date);
      }
    } else {
      dateObj = new Date(date);
    }

    // Geçerli bir tarih değilse
    if (isNaN(dateObj.getTime())) return '-';

    // Türkiye timezone'ı kullanarak formatla
    const options: Intl.DateTimeFormatOptions = {
      timeZone: 'Europe/Istanbul',
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      ...(includeTime && {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      })
    };

    return dateObj.toLocaleString('tr-TR', options);
  } catch (error) {
    console.error('Tarih formatlanırken hata oluştu:', error);
    return '-';
  }
};

/**
 * Verilen tarihi Türkiye saatine (UTC+3) göre sadece tarih olarak formatlar
 * @param date - Formatlanacak tarih
 * @returns Formatlanmış tarih
 */
export const formatTurkishDate = (date: string | Date | null | undefined): string => {
  return formatTurkishDateTime(date, false);
};

/**
 * Verilen tarihi Türkiye saatine (UTC+3) göre sadece saat olarak formatlar
 * @param date - Formatlanacak tarih
 * @returns Formatlanmış saat
 */
export const formatTurkishTime = (date: string | Date | null | undefined): string => {
  if (!date) return '-';

  try {
    // Tarih nesnesine çevir - UTC olarak parse et
    let dateObj: Date;
    if (typeof date === 'string') {
      // Eğer string UTC formatında değilse, UTC olarak parse et
      if (!date.includes('Z') && !date.includes('+')) {
        dateObj = new Date(date + 'Z'); // UTC olarak parse et
      } else {
        dateObj = new Date(date);
      }
    } else {
      dateObj = new Date(date);
    }

    // Geçerli bir tarih değilse
    if (isNaN(dateObj.getTime())) return '-';

    // Türkiye timezone'ı kullanarak sadece saat formatla
    const timeOptions: Intl.DateTimeFormatOptions = {
      timeZone: 'Europe/Istanbul',
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    };

    return dateObj.toLocaleTimeString('tr-TR', timeOptions);
  } catch (error) {
    console.error('Saat formatlanırken hata oluştu:', error);
    return '-';
  }
};

/**
 * Para birimini Türk Lirası formatında gösterir
 * @param value - Formatlanacak değer
 * @returns Formatlanmış para birimi
 */
export const formatCurrency = (value: number | string | null | undefined): string => {
  if (value === null || value === undefined) return '0,00 ₺';
  
  try {
    const numValue = parseFloat(String(value));
    if (isNaN(numValue)) return '0,00 ₺';
    
    return numValue.toLocaleString('tr-TR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }) + ' ₺';
  } catch (error) {
    console.error('Para birimi formatlanırken hata oluştu:', error);
    return '0,00 ₺';
  }
};
