import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  FlatList,
  ActivityIndicator,
  RefreshControl,
  Switch,
  Alert,
  TextInput,
  Image
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { StackNavigationProp } from '@react-navigation/stack';
import { getMenuItems, getCategories, changeMenuItemVisibility } from '../config/api';
import { formatCurrency } from '../utils/dateUtils';
import { RootStackParamList } from '../types';

type MenuManagementScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MenuManagement'>;

interface MenuManagementScreenProps {
  navigation: MenuManagementScreenNavigationProp;
}

interface MenuItem {
  id: number;
  title: string;
  description: string | null;
  price: string;
  net_price: string | null;
  category_id: number | null;
  category_title: string | null;
  image: string | null;
  is_enabled: number;
  sort_order: number;
  addons: any[];
  variants: any[];
}

interface Category {
  id: number;
  title: string;
  cat_image: string | null;
  sort_order: number;
  printer: {
    id: number;
    name: string;
  };
  parent_id: number | null;
  parent_title: string | null;
}

const MenuManagementScreen: React.FC<MenuManagementScreenProps> = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [menuItems, setMenuItems] = useState<MenuItem[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [filteredItems, setFilteredItems] = useState<MenuItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [searchQuery, setSearchQuery] = useState<string>('');

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    filterItems();
  }, [menuItems, selectedCategory, searchQuery]);

  const fetchData = async (): Promise<void> => {
    try {
      setIsLoading(true);
      const [menuItemsData, categoriesData] = await Promise.all([
        getMenuItems(),
        getCategories()
      ]);
      
      setMenuItems(menuItemsData || []);
      setCategories(categoriesData || []);
    } catch (error) {
      console.error('Error fetching menu data:', error);
      Alert.alert('Hata', 'Menü verileri alınamadı. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const filterItems = (): void => {
    let filtered = menuItems;

    // Kategori filtresi
    if (selectedCategory !== null) {
      filtered = filtered.filter(item => item.category_id === selectedCategory);
    }

    // Arama filtresi
    if (searchQuery.trim()) {
      filtered = filtered.filter(item =>
        item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (item.description && item.description.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    setFilteredItems(filtered);
  };

  const onRefresh = (): void => {
    setRefreshing(true);
    fetchData();
  };

  const handleVisibilityToggle = async (item: MenuItem): Promise<void> => {
    try {
      const newVisibility = item.is_enabled === 1 ? 0 : 1;
      await changeMenuItemVisibility(item.id, newVisibility);
      
      // Local state'i güncelle
      setMenuItems(prevItems =>
        prevItems.map(menuItem =>
          menuItem.id === item.id
            ? { ...menuItem, is_enabled: newVisibility }
            : menuItem
        )
      );
      
      Alert.alert(
        'Başarılı',
        `${item.title} ${newVisibility === 1 ? 'aktif' : 'pasif'} hale getirildi.`
      );
    } catch (error) {
      console.error('Error toggling visibility:', error);
      Alert.alert('Hata', 'Görünürlük değiştirilemedi. Lütfen tekrar deneyin.');
    }
  };

  const handleEditItem = (item: MenuItem): void => {
    navigation.navigate('MenuItemEdit', { itemId: item.id });
  };

  const renderCategoryFilter = () => {
    const allCategories = [
      { id: null, title: 'Tümü' },
      ...categories
    ];

    return (
      <View style={styles.categoryFilterContainer}>
        <FlatList
          horizontal
          data={allCategories}
          keyExtractor={(item) => item.id?.toString() || 'all'}
          showsHorizontalScrollIndicator={false}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.categoryFilterButton,
                selectedCategory === item.id && styles.selectedCategoryFilter
              ]}
              onPress={() => setSelectedCategory(item.id)}
            >
              <Text style={[
                styles.categoryFilterText,
                selectedCategory === item.id && styles.selectedCategoryFilterText
              ]}>
                {item.title}
              </Text>
            </TouchableOpacity>
          )}
        />
      </View>
    );
  };

  const renderMenuItem = ({ item }: { item: MenuItem }) => (
    <View style={styles.menuItemCard}>
      <View style={styles.menuItemHeader}>
        <View style={styles.menuItemInfo}>
          <Text style={styles.menuItemTitle}>{item.title}</Text>
          {item.description && (
            <Text style={styles.menuItemDescription} numberOfLines={2}>
              {item.description}
            </Text>
          )}
          <View style={styles.menuItemDetails}>
            <Text style={styles.menuItemPrice}>
              {formatCurrency(parseFloat(item.price))}
            </Text>
            {item.category_title && (
              <Text style={styles.menuItemCategory}>
                {item.category_title}
              </Text>
            )}
          </View>
        </View>
        
        {item.image && (
          <Image
            source={{ uri: `https://backendpos.hollystone.com.tr${item.image}` }}
            style={styles.menuItemImage}
            resizeMode="cover"
          />
        )}
      </View>

      <View style={styles.menuItemActions}>
        <View style={styles.visibilityContainer}>
          <Text style={styles.visibilityLabel}>
            {item.is_enabled === 1 ? 'Aktif' : 'Pasif'}
          </Text>
          <Switch
            value={item.is_enabled === 1}
            onValueChange={() => handleVisibilityToggle(item)}
            trackColor={{ false: '#767577', true: '#4CAF50' }}
            thumbColor={item.is_enabled === 1 ? '#fff' : '#f4f3f4'}
          />
        </View>
        
        <TouchableOpacity
          style={styles.editButton}
          onPress={() => handleEditItem(item)}
        >
          <Icon name="edit" size={20} color="#2196F3" />
          <Text style={styles.editButtonText}>Düzenle</Text>
        </TouchableOpacity>
      </View>

      {(item.variants.length > 0 || item.addons.length > 0) && (
        <View style={styles.menuItemExtras}>
          {item.variants.length > 0 && (
            <Text style={styles.extrasText}>
              {item.variants.length} varyant
            </Text>
          )}
          {item.addons.length > 0 && (
            <Text style={styles.extrasText}>
              {item.addons.length} eklenti
            </Text>
          )}
        </View>
      )}
    </View>
  );

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Menü Yönetimi</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Menü öğeleri yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Menü Yönetimi</Text>
      </View>

      <View style={styles.searchContainer}>
        <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
        <TextInput
          style={styles.searchInput}
          placeholder="Menü öğesi ara..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
      </View>

      {renderCategoryFilter()}

      <View style={styles.statsContainer}>
        <Text style={styles.statsText}>
          Toplam {filteredItems.length} öğe
        </Text>
        <Text style={styles.statsText}>
          Aktif: {filteredItems.filter(item => item.is_enabled === 1).length}
        </Text>
      </View>

      <FlatList
        data={filteredItems}
        renderItem={renderMenuItem}
        keyExtractor={(item) => item.id.toString()}
        style={styles.menuList}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.menuListContent}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  backButton: {
    padding: 5,
    marginRight: 10,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#fff',
    margin: 15,
    paddingHorizontal: 15,
    paddingVertical: 10,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  searchIcon: {
    marginRight: 10,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: '#333',
  },
  categoryFilterContainer: {
    backgroundColor: '#fff',
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  categoryFilterButton: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginHorizontal: 5,
    backgroundColor: '#f0f0f0',
    borderRadius: 20,
  },
  selectedCategoryFilter: {
    backgroundColor: '#2196F3',
  },
  categoryFilterText: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  selectedCategoryFilterText: {
    color: '#fff',
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingHorizontal: 15,
    paddingVertical: 10,
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  statsText: {
    fontSize: 14,
    color: '#666',
    fontWeight: '500',
  },
  menuList: {
    flex: 1,
  },
  menuListContent: {
    padding: 15,
  },
  menuItemCard: {
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  menuItemHeader: {
    flexDirection: 'row',
    marginBottom: 15,
  },
  menuItemInfo: {
    flex: 1,
    marginRight: 10,
  },
  menuItemTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  menuItemDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 8,
    lineHeight: 20,
  },
  menuItemDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  menuItemPrice: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  menuItemCategory: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#f0f0f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 10,
  },
  menuItemImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
  },
  menuItemActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  visibilityContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  visibilityLabel: {
    fontSize: 14,
    color: '#333',
    fontWeight: '500',
  },
  editButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 5,
    paddingHorizontal: 12,
    paddingVertical: 6,
    backgroundColor: '#f0f8ff',
    borderRadius: 6,
  },
  editButtonText: {
    fontSize: 14,
    color: '#2196F3',
    fontWeight: '500',
  },
  menuItemExtras: {
    flexDirection: 'row',
    gap: 10,
    marginTop: 10,
  },
  extrasText: {
    fontSize: 12,
    color: '#666',
    backgroundColor: '#fff3cd',
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
  },
});

export default MenuManagementScreen;
