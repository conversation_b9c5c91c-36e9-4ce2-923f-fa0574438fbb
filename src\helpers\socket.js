import io from 'socket.io-client';

const SOCKET_URL = 'https://backendpos.hollystone.com.tr';

let socket = null;

export const initSocket = () => {
  if (!socket || !socket.connected) {
    socket = io(SOCKET_URL, {
      autoConnect: false,
      rejectUnauthorized: false,
      transports: ['websocket', 'polling'], // Fallback to polling if websocket fails
      timeout: 20000,
      forceNew: true
    });
    
    socket.connect();
    
    // Connection event listeners
    socket.on('connect', () => {
      console.log('Socket connected:', socket.id);
    });
    
    socket.on('disconnect', (reason) => {
      console.log('Socket disconnected:', reason);
    });
    
    socket.on('connect_error', (error) => {
      console.error('Socket connection error:', error);
    });
  }
  
  return socket;
};

export const disconnectSocket = () => {
  if (socket && socket.connected) {
    socket.disconnect();
    socket = null;
  }
};

export const getSocket = () => {
  return socket;
};

export const isSocketConnected = () => {
  return socket && socket.connected;
};

export default {
  initSocket,
  disconnectSocket,
  getSocket,
  isSocketConnected
};
