import io from 'socket.io-client';

const SOCKET_URL = 'https://hollypos-backend.uygulama.sewpos.com';

let socket = null;

export const initSocket = () => {
  console.log('initSocket called, current socket state:', socket ? 'exists' : 'null');

  if (!socket || !socket.connected) {
    console.log('Creating new socket connection to:', SOCKET_URL);

    socket = io(SOCKET_URL, {
      autoConnect: false,
      rejectUnauthorized: false,
      transports: ['websocket', 'polling'], // Fallback to polling if websocket fails
      timeout: 20000,
      forceNew: true
    });

    console.log('Socket created, attempting to connect...');
    socket.connect();

    // Connection event listeners
    socket.on('connect', () => {
      console.log('✅ Socket connected successfully:', socket.id);
    });

    socket.on('disconnect', (reason) => {
      console.log('❌ Socket disconnected:', reason);
    });

    socket.on('connect_error', (error) => {
      console.error('🔥 Socket connection error:', error);
    });
  } else {
    console.log('Socket already connected:', socket.id);
  }

  return socket;
};

export const disconnectSocket = () => {
  if (socket && socket.connected) {
    socket.disconnect();
    socket = null;
  }
};

export const getSocket = () => {
  return socket;
};

export const isSocketConnected = () => {
  return socket && socket.connected;
};

export default {
  initSocket,
  disconnectSocket,
  getSocket,
  isSocketConnected
};
