{"name": "SewPOS", "version": "1.0.0", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@react-native-async-storage/async-storage": "^2.0.0", "@react-native-community/datetimepicker": "^8.4.1", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "axios": "^1.7.7", "react": "18.2.0", "react-native": "0.74.0", "react-native-axios": "^0.17.1", "react-native-date-picker": "^5.0.12", "react-native-gesture-handler": "^2.20.1", "react-native-keychain": "^9.0.0", "react-native-safe-area-context": "^4.14.0", "react-native-screens": "^4.0.0", "react-native-vector-icons": "^10.2.0", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "0.74.81", "@react-native/eslint-config": "0.74.81", "@react-native/metro-config": "0.74.81", "@react-native/typescript-config": "0.74.81", "@types/react": "^18.2.6", "@types/react-native-vector-icons": "^6.4.18", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.2.0", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}