import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
  ActivityIndicator,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useNavigation, useRoute, RouteProp } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RootStackParamList } from '../types';
import { getReports } from '../config/api';
import { formatCurrency, formatTurkishDateTime } from '../utils/dateUtils';

type NavigationProp = StackNavigationProp<RootStackParamList, 'OpenOrdersDetail'>;
type RoutePropType = RouteProp<RootStackParamList, 'OpenOrdersDetail'>;

const OpenOrdersDetailScreen: React.FC = () => {
  const navigation = useNavigation<NavigationProp>();
  const route = useRoute<RoutePropType>();
  const [reportData, setReportData] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async (): Promise<void> => {
    try {
      setLoading(true);
      const { reportType, startDate, endDate } = route.params;

      let response;
      if (reportType === 'custom' && startDate && endDate) {
        response = await getReports(reportType, startDate, endDate);
      } else {
        response = await getReports(reportType);
      }

      setReportData(response);
    } catch (error) {
      console.error('Error fetching report data:', error);
      setReportData(null);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async (): Promise<void> => {
    setRefreshing(true);
    await fetchData();
    setRefreshing(false);
  };

  const calculateTotal = (): number => {
    if (!reportData?.openOrders) return 0;
    let total = 0;
    Object.values(reportData.openOrders).forEach((orders: any) => {
      orders.forEach((order: any) => {
        total += parseFloat(order.order_total || 0);
      });
    });
    return total;
  };

  const getDeliveryTypeTitle = (type: string): string => {
    switch (type) {
      case 'dinein': return 'Masa Siparişi';
      case 'takeaway': return 'Paket Servis';
      case 'delivery': return 'Teslimat';
      default: return 'Bilinmeyen';
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Açık Siparişler</Text>
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Açık Siparişler</Text>
      </View>

      <View style={styles.summaryContainer}>
        <View style={styles.summaryCard}>
          <Icon name="restaurant" size={24} color="#2196F3" />
          <Text style={styles.summaryLabel}>Toplam Tutar</Text>
          <Text style={styles.summaryValue}>{formatCurrency(calculateTotal())}</Text>
        </View>
        <View style={styles.summaryCard}>
          <Icon name="list-alt" size={24} color="#4CAF50" />
          <Text style={styles.summaryLabel}>Sipariş Sayısı</Text>
          <Text style={styles.summaryValue}>
            {reportData?.openOrders ?
              Object.values(reportData.openOrders).reduce((total: number, orders: any) => total + orders.length, 0) : 0
            }
          </Text>
        </View>
      </View>

      <ScrollView
        style={styles.scrollContainer}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {reportData?.openOrders && Object.keys(reportData.openOrders).length > 0 ? (
          Object.entries(reportData.openOrders).map(([deliveryType, orders]: [string, any]) => (
            orders.length > 0 && (
              <View key={deliveryType} style={styles.sectionContainer}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>{getDeliveryTypeTitle(deliveryType)}</Text>
                  <Text style={styles.sectionCount}>{orders.length} sipariş</Text>
                </View>

                {orders.map((order: any, index: number) => (
                  <View key={index} style={styles.orderItem}>
                    <View style={styles.orderHeader}>
                      <Text style={styles.orderNumber}>#{order.order_id}</Text>
                      <Text style={styles.orderTotal}>{formatCurrency(order.order_total)}</Text>
                    </View>

                    <View style={styles.orderInfo}>
                      <Text style={styles.orderTable}>{order.table_title}</Text>
                      <Text style={styles.orderTime}>{formatTurkishDateTime(order.order_date)}</Text>
                    </View>

                    {order.customer_name && (
                      <Text style={styles.customerName}>Müşteri: {order.customer_name}</Text>
                    )}

                    <Text style={styles.createdBy}>
                      Oluşturan: {order.created_by_name} ({order.created_by_username})
                    </Text>

                    {order.items && order.items.length > 0 && (
                      <View style={styles.itemsContainer}>
                        <Text style={styles.itemsTitle}>Ürünler ({order.items.length}):</Text>
                        {order.items.slice(0, 3).map((item: any, itemIndex: number) => (
                          <Text key={itemIndex} style={styles.itemText}>
                            • {item.item_title} {item.variant_title ? `(${item.variant_title})` : ''}
                            - {item.quantity}x {formatCurrency(parseFloat(item.price))}
                            {item.status !== 'created' && (
                              <Text style={[styles.itemStatus,
                                item.status === 'complimentary' ? styles.complimentaryStatus :
                                item.status === 'waste' ? styles.wasteStatus : styles.defaultStatus
                              ]}>
                                {' '}[{item.status === 'complimentary' ? 'İkram' :
                                       item.status === 'waste' ? 'Zayi' : item.status}]
                              </Text>
                            )}
                          </Text>
                        ))}
                        {order.items.length > 3 && (
                          <Text style={styles.moreItems}>
                            +{order.items.length - 3} ürün daha...
                          </Text>
                        )}
                      </View>
                    )}
                  </View>
                ))}
              </View>
            )
          ))
        ) : (
          <View style={styles.emptyContainer}>
            <Icon name="inbox" size={60} color="#ccc" />
            <Text style={styles.emptyText}>Açık sipariş bulunmuyor</Text>
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    paddingTop: 16,
  },
  backButton: {
    marginRight: 16,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  summaryContainer: {
    flexDirection: 'row',
    padding: 16,
    gap: 12,
  },
  summaryCard: {
    flex: 1,
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  summaryLabel: {
    fontSize: 12,
    color: '#666',
    marginTop: 8,
    marginBottom: 4,
  },
  summaryValue: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  scrollContainer: {
    flex: 1,
  },
  sectionContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  sectionCount: {
    fontSize: 14,
    color: '#666',
  },
  orderItem: {
    backgroundColor: '#fff',
    padding: 16,
    borderRadius: 8,
    marginBottom: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  orderHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  orderNumber: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  orderTotal: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  orderInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  orderTable: {
    fontSize: 14,
    color: '#666',
  },
  orderTime: {
    fontSize: 12,
    color: '#999',
  },
  customerName: {
    fontSize: 14,
    color: '#333',
    marginBottom: 4,
  },
  createdBy: {
    fontSize: 12,
    color: '#999',
    marginBottom: 8,
  },
  itemsContainer: {
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  itemsTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  itemText: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  itemStatus: {
    fontSize: 11,
    fontWeight: 'bold',
  },
  complimentaryStatus: {
    color: '#9C27B0',
  },
  wasteStatus: {
    color: '#FF9800',
  },
  defaultStatus: {
    color: '#666',
  },
  moreItems: {
    fontSize: 12,
    color: '#2196F3',
    fontStyle: 'italic',
    marginTop: 4,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
  },
  emptyText: {
    fontSize: 16,
    color: '#999',
    marginTop: 10,
    textAlign: 'center',
  },
});

export default OpenOrdersDetailScreen;
