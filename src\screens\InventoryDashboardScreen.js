import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  TouchableOpacity,
  TextInput,
  ActivityIndicator,
  Modal,
  Alert,
  RefreshControl,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { getInventoryDashboard } from '../config/api';
import { formatTurkishDate } from '../utils/dateUtils';

const InventoryDashboardScreen = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [error, setError] = useState(null);
  const [data, setData] = useState(null);
  const [searchText, setSearchText] = useState('');
  const [filterModalVisible, setFilterModalVisible] = useState(false);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [datePickerMode, setDatePickerMode] = useState('start');

  const filters = [
    { key: 'today', value: 'Bugün' },
    { key: 'yesterday', value: 'Dün' },
    { key: 'last_7days', value: 'Son 7 Gün' },
    { key: 'this_month', value: 'Bu Ay' },
    { key: 'last_month', value: 'Geçen Ay' },
    { key: 'custom', value: 'Özel' },
  ];

  const [filterState, setFilterState] = useState({
    filter: 'today',
    fromDate: new Date(),
    toDate: new Date(),
  });

  useEffect(() => {
    fetchInventoryData();
  }, [filterState]);

  const fetchInventoryData = async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      const result = await getInventoryDashboard({
        type: filterState.filter,
        from: filterState.filter === 'custom' ? formatDate(filterState.fromDate) : null,
        to: filterState.filter === 'custom' ? formatDate(filterState.toDate) : null,
      });
      
      setData(result);
    } catch (err) {
      setError('Veri yüklenirken bir hata oluştu!');
      console.error('Inventory dashboard error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchInventoryData();
    setRefreshing(false);
  };

  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${year}-${month}-${day}`;
  };



  const onDateChange = (_, selectedDate) => {
    const currentDate = selectedDate || (datePickerMode === 'start' ? filterState.fromDate : filterState.toDate);
    setShowDatePicker(false);

    if (datePickerMode === 'start') {
      setFilterState(prev => ({ ...prev, fromDate: currentDate }));
    } else {
      setFilterState(prev => ({ ...prev, toDate: currentDate }));
    }
  };

  const showStartDatePicker = () => {
    setDatePickerMode('start');
    setShowDatePicker(true);
  };

  const showEndDatePicker = () => {
    setDatePickerMode('end');
    setShowDatePicker(true);
  };

  const applyFilter = (selectedFilter) => {
    setFilterState(prev => ({ ...prev, filter: selectedFilter }));
    setFilterModalVisible(false);
  };

  const filterItems = (items) => {
    if (!searchText.trim()) return items;
    return items.filter(item =>
      item.title.toLowerCase().includes(searchText.toLowerCase())
    );
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'in': return '#4CAF50';
      case 'low': return '#FF9800';
      case 'out': return '#F44336';
      default: return '#666';
    }
  };

  const getStatusText = (status) => {
    switch (status) {
      case 'in': return 'Stokta';
      case 'low': return 'Stok Az';
      case 'out': return 'Stokta Yok';
      default: return 'Bilinmiyor';
    }
  };

  if (isLoading) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Yükleniyor...</Text>
        </View>
      </SafeAreaView>
    );
  }

  if (error) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={60} color="#F44336" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchInventoryData}>
            <Text style={styles.retryButtonText}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <View style={styles.headerRow}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Envanter Paneli</Text>
        </View>

        {/* Search and Filter */}
        <View style={styles.searchFilterRow}>
          <View style={styles.searchContainer}>
            <Icon name="search" size={20} color="#666" style={styles.searchIcon} />
            <TextInput
              style={styles.searchInput}
              placeholder="Ara..."
              value={searchText}
              onChangeText={setSearchText}
            />
          </View>
          
          <TouchableOpacity
            style={styles.filterButton}
            onPress={() => setFilterModalVisible(true)}
          >
            <Icon name="filter-list" size={20} color="#fff" />
            <Text style={styles.filterButtonText}>Filtreler</Text>
          </TouchableOpacity>
        </View>

        <Text style={styles.filterInfo}>
          Gösterilen veriler: {filters.find(f => f.key === filterState.filter)?.value}
        </Text>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        showsVerticalScrollIndicator={false}
        scrollEventThrottle={16}
      >
        {/* Kümülatif Envanter Hareketleri */}
        {data?.cummulativeInventoryMovements && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Kümülatif Envanter Hareketleri</Text>
            
            <View style={styles.tableContainer}>
              <View style={styles.tableHeader}>
                <Text style={[styles.tableHeaderText, { flex: 0.5 }]}>No</Text>
                <Text style={[styles.tableHeaderText, { flex: 2 }]}>Ürün</Text>
                <Text style={[styles.tableHeaderText, { flex: 1, color: '#4CAF50' }]}>Giriş</Text>
                <Text style={[styles.tableHeaderText, { flex: 1, color: '#F44336' }]}>Çıkış</Text>
                <Text style={[styles.tableHeaderText, { flex: 1, color: '#FF9800' }]}>Fire</Text>
              </View>

              {filterItems(data.cummulativeInventoryMovements).map((item, index) => (
                <View key={item.inventory_item_id} style={styles.tableRow}>
                  <Text style={[styles.tableCell, { flex: 0.5 }]}>{index + 1}</Text>
                  <Text style={[styles.tableCell, { flex: 2 }]}>{item.title}</Text>
                  <View style={[styles.tableCell, { flex: 1 }]}>
                    <Text style={styles.greenText}>{item.total_in} {item.unit}</Text>
                  </View>
                  <View style={[styles.tableCell, { flex: 1 }]}>
                    <Text style={styles.redText}>{item.total_out} {item.unit}</Text>
                  </View>
                  <View style={[styles.tableCell, { flex: 1 }]}>
                    <Text style={styles.orangeText}>
                      {item.total_wastage > 0 ? `${item.total_wastage} ${item.unit}` : 'Yok'}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Kullanım vs Mevcut Stok */}
        {data?.inventoryUsageVSCurrentStock && (
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Kullanım vs Mevcut Stok</Text>
            
            <View style={styles.tableContainer}>
              <View style={styles.tableHeader}>
                <Text style={[styles.tableHeaderText, { flex: 0.5 }]}>No</Text>
                <Text style={[styles.tableHeaderText, { flex: 1.5 }]}>Ürün</Text>
                <Text style={[styles.tableHeaderText, { flex: 1, color: '#F44336' }]}>Kullanılan</Text>
                <Text style={[styles.tableHeaderText, { flex: 1, color: '#4CAF50' }]}>Mevcut</Text>
                <Text style={[styles.tableHeaderText, { flex: 1, color: '#FF9800' }]}>Min.</Text>
                <Text style={[styles.tableHeaderText, { flex: 1 }]}>Durum</Text>
              </View>

              {filterItems(data.inventoryUsageVSCurrentStock).map((item, index) => (
                <View key={index} style={styles.tableRow}>
                  <Text style={[styles.tableCell, { flex: 0.5 }]}>{index + 1}</Text>
                  <Text style={[styles.tableCell, { flex: 1.5 }]}>{item.title}</Text>
                  <View style={[styles.tableCell, { flex: 1 }]}>
                    <Text style={styles.redText}>{item.total_usage} {item.unit}</Text>
                  </View>
                  <View style={[styles.tableCell, { flex: 1 }]}>
                    <Text style={styles.greenText}>{item.current_stock} {item.unit}</Text>
                  </View>
                  <View style={[styles.tableCell, { flex: 1 }]}>
                    <Text style={styles.orangeText}>{Number(item.min_quantity_threshold).toFixed(2)} {item.unit}</Text>
                  </View>
                  <View style={[styles.tableCell, { flex: 1 }]}>
                    <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) + '20' }]}>
                      <Text style={[styles.statusText, { color: getStatusColor(item.status) }]}>
                        {getStatusText(item.status)}
                      </Text>
                    </View>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}
      </ScrollView>

      {/* Filter Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={filterModalVisible}
        onRequestClose={() => setFilterModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Filtreler</Text>
              <TouchableOpacity onPress={() => setFilterModalVisible(false)}>
                <Icon name="close" size={24} color="#333" />
              </TouchableOpacity>
            </View>

            <View style={styles.filterOptions}>
              {filters.map((filter) => (
                <TouchableOpacity
                  key={filter.key}
                  style={[
                    styles.filterOption,
                    filterState.filter === filter.key && styles.selectedFilterOption
                  ]}
                  onPress={() => applyFilter(filter.key)}
                >
                  <Text style={[
                    styles.filterOptionText,
                    filterState.filter === filter.key && styles.selectedFilterOptionText
                  ]}>
                    {filter.value}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>

            {filterState.filter === 'custom' && (
              <View style={styles.datePickerContainer}>
                <View style={styles.dateRow}>
                  <Text style={styles.dateLabel}>Başlangıç:</Text>
                  <TouchableOpacity style={styles.dateButton} onPress={showStartDatePicker}>
                    <Text style={styles.dateButtonText}>{formatTurkishDate(filterState.fromDate)}</Text>
                    <Icon name="calendar-today" size={16} color="#2196F3" />
                  </TouchableOpacity>
                </View>

                <View style={styles.dateRow}>
                  <Text style={styles.dateLabel}>Bitiş:</Text>
                  <TouchableOpacity style={styles.dateButton} onPress={showEndDatePicker}>
                    <Text style={styles.dateButtonText}>{formatTurkishDate(filterState.toDate)}</Text>
                    <Icon name="calendar-today" size={16} color="#2196F3" />
                  </TouchableOpacity>
                </View>
              </View>
            )}
          </View>
        </View>
      </Modal>

      {/* Date Picker */}
      {showDatePicker && (
        <DateTimePicker
          value={datePickerMode === 'start' ? filterState.fromDate : filterState.toDate}
          mode="date"
          display="default"
          onChange={onDateChange}
        />
      )}
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#F44336',
    textAlign: 'center',
    marginVertical: 10,
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  header: {
    backgroundColor: '#fff',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  backButton: {
    marginRight: 12,
    padding: 4,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  searchFilterRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  searchContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderRadius: 8,
    paddingHorizontal: 12,
    marginRight: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    height: 40,
    fontSize: 16,
    color: '#333',
  },
  filterButton: {
    backgroundColor: '#4CAF50',
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
  },
  filterButtonText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
    marginLeft: 4,
  },
  filterInfo: {
    fontSize: 14,
    color: '#666',
  },
  content: {
    flex: 1,
    padding: 16,
  },
  section: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 16,
    marginBottom: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 16,
  },
  tableContainer: {
    borderWidth: 1,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    overflow: 'hidden',
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f5f5f5',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  tableHeaderText: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#666',
    textAlign: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    paddingVertical: 12,
    paddingHorizontal: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  tableCell: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  greenText: {
    color: '#4CAF50',
    fontSize: 12,
    fontWeight: 'bold',
  },
  redText: {
    color: '#F44336',
    fontSize: 12,
    fontWeight: 'bold',
  },
  orangeText: {
    color: '#FF9800',
    fontSize: 12,
    fontWeight: 'bold',
  },
  statusBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  statusText: {
    fontSize: 10,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    width: '90%',
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
  },
  filterOptions: {
    marginBottom: 20,
  },
  filterOption: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginBottom: 8,
    backgroundColor: '#f5f5f5',
  },
  selectedFilterOption: {
    backgroundColor: '#2196F3',
  },
  filterOptionText: {
    fontSize: 16,
    color: '#333',
  },
  selectedFilterOptionText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  datePickerContainer: {
    marginTop: 16,
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  dateLabel: {
    fontSize: 14,
    color: '#666',
    width: 80,
  },
  dateButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: '#f5f5f5',
    paddingHorizontal: 12,
    paddingVertical: 10,
    borderRadius: 8,
    marginLeft: 12,
  },
  dateButtonText: {
    fontSize: 14,
    color: '#333',
  },
});

export default InventoryDashboardScreen;
