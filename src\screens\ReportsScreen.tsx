import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  ScrollView,
  ActivityIndicator,
  RefreshControl,
  FlatList,
  Modal,
  Platform,
  Alert,
  TextInput
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { StackNavigationProp } from '@react-navigation/stack';
import { getReports, getLastCashRegisterSessions, getOrderDetails } from '../config/api';
import { formatTurkishDateTime, formatTurkishDate, formatTurkishTime, formatCurrency } from '../utils/dateUtils';
import { RootStackParamList, SalesReport, CashRegisterData } from '../types';

type ReportsScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Reports'>;

interface ReportsScreenProps {
  navigation: ReportsScreenNavigationProp;
}

type ReportType = 'today' | 'yesterday' | 'this_month' | 'last_month' | 'last_7days' | 'custom';

interface ExtendedSalesReport extends SalesReport {
  ordersCount?: number;
  averageOrderValue?: number;
  revenueTotal?: number;
  netRevenue?: number;
  taxTotal?: number;
  getTotalDiscounts?: { total: number } | number;
  cancelledOrdersAndItems?: { total: number };
  creditTotal?: { total: number } | number;
  complimentaryTotal?: { total: number } | number;
  wasteTotal?: { total: number } | number;
  openOrdersTotal?: number;
  openOrders?: Record<string, any[]>;
  totalPaymentsByPaymentTypes?: Array<{ title: string; total: number }>;
  topSellingItems?: Array<{ title: string; orders_count: number; price: number }>;
  netRevenueByFloor?: Array<{
    floor_title: string;
    net_revenue: string;
    order_count: number;
    avg_order_value: string;
    customer_count: number;
  }>;
}

const ReportsScreen: React.FC<ReportsScreenProps> = ({ navigation }) => {
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [refreshing, setRefreshing] = useState<boolean>(false);
  const [reportType, setReportType] = useState<ReportType>('today');
  const [reportData, setReportData] = useState<ExtendedSalesReport | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [showDatePicker, setShowDatePicker] = useState<boolean>(false);
  const [startDate, setStartDate] = useState<Date>(new Date());
  const [endDate, setEndDate] = useState<Date>(new Date());
  const [datePickerMode, setDatePickerMode] = useState<'start' | 'end'>('start');
  const [timePickerMode, setTimePickerMode] = useState<'date' | 'time'>('date');
  const [lastCashSessions, setLastCashSessions] = useState<any[]>([]);
  const [loadingLastCashSessions, setLoadingLastCashSessions] = useState<boolean>(false);

  useEffect(() => {
    fetchReportData();
    fetchLastCashRegisterSessions();
  }, [reportType]);

  const fetchLastCashRegisterSessions = async (): Promise<void> => {
    try {
      setLoadingLastCashSessions(true);
      const data = await getLastCashRegisterSessions();

      if (data && (data as any).success && Array.isArray((data as any).data)) {
        setLastCashSessions((data as any).data);
      } else if (data && Array.isArray(data)) {
        setLastCashSessions(data);
      } else if (data && typeof data === 'object') {
        if (Array.isArray((data as any).data)) {
          setLastCashSessions((data as any).data);
        } else {
          setLastCashSessions([data]);
        }
      } else {
        setLastCashSessions([]);
      }
    } catch (error) {
      console.error('Error fetching last cash register sessions:', error);
      setLastCashSessions([]);
    } finally {
      setLoadingLastCashSessions(false);
    }
  };

  const fetchReportData = async (): Promise<void> => {
    try {
      setIsLoading(true);
      setError(null);
      let data: ExtendedSalesReport;

      if (reportType === 'custom') {
        // Tarih ve saat bilgisini ISO formatında gönder
        const formattedStartDate = startDate.toISOString();
        const formattedEndDate = endDate.toISOString();
        data = await getReports(reportType, formattedStartDate, formattedEndDate) as ExtendedSalesReport;
      } else {
        data = await getReports(reportType) as ExtendedSalesReport;
      }

      setReportData(data);
    } catch (error) {
      console.error('Error fetching report data:', error);
      setError('Rapor verileri alınamadı. Lütfen tekrar deneyin.');
    } finally {
      setIsLoading(false);
      setRefreshing(false);
    }
  };

  const onRefresh = (): void => {
    setRefreshing(true);
    fetchLastCashRegisterSessions();
    fetchReportData();
  };

  const calculateOpenOrdersTotal = (): number => {
    if (!reportData?.openOrders) return 0;

    let total = 0;
    Object.values(reportData.openOrders).forEach(orders => {
      orders.forEach(order => {
        total += parseFloat(order.order_total || 0);
      });
    });

    return total;
  };

  const getNavigationParams = () => {
    const params: any = {
      reportType: reportType
    };

    if (reportType === 'custom') {
      params.startDate = startDate.toISOString();
      params.endDate = endDate.toISOString();
    }

    return params;
  };

  const handleOpenOrdersClick = (): void => {
    navigation.navigate('OpenOrdersDetail', {
      openOrders: reportData?.openOrders || { dinein: [], takeaway: [], delivery: [], unknown: [] },
      reportType,
      startDate: startDate?.toISOString(),
      endDate: endDate?.toISOString()
    });
  };

  const handleWaiterPerformanceClick = (): void => {
    navigation.navigate('WaiterPerformance', getNavigationParams());
  };

  const handleCancelledDetailsClick = (): void => {
    navigation.navigate('CancelledOrdersDetail', getNavigationParams());
  };

  const handleWasteDetailsClick = (): void => {
    navigation.navigate('WasteDetail', getNavigationParams());
  };

  const handleComplimentaryDetailsClick = (): void => {
    navigation.navigate('ComplimentaryDetail', getNavigationParams());
  };

  const handleDiscountDetailsClick = (): void => {
    navigation.navigate('DiscountDetail', getNavigationParams());
  };

  const handleCreditDetailsClick = (): void => {
    navigation.navigate('CreditDetail', getNavigationParams());
  };

  const handleCashSessionClick = (session: any): void => {
    if (session && session.id) {
      navigation.navigate('CashRegisterDetail', { sessionId: session.id });
    }
  };

  const fetchOrderDetails = (): void => {
    navigation.navigate('OrderDetails', getNavigationParams());
  };

  const showStartDatePicker = (): void => {
    setDatePickerMode('start');
    setTimePickerMode('date');
    setShowDatePicker(true);
  };

  const showEndDatePicker = (): void => {
    setDatePickerMode('end');
    setTimePickerMode('date');
    setShowDatePicker(true);
  };

  const showStartTimePicker = (): void => {
    setDatePickerMode('start');
    setTimePickerMode('time');
    setShowDatePicker(true);
  };

  const showEndTimePicker = (): void => {
    setDatePickerMode('end');
    setTimePickerMode('time');
    setShowDatePicker(true);
  };

  const onDateChange = (_: any, selectedDate?: Date): void => {
    const currentDate = selectedDate || (datePickerMode === 'start' ? startDate : endDate);
    
    if (Platform.OS === 'android') {
      setShowDatePicker(false);
    }

    if (datePickerMode === 'start') {
      setStartDate(currentDate);
    } else {
      setEndDate(currentDate);
    }

    // Eğer tarih seçildi ve Android ise, otomatik olarak saat seçici aç
    if (Platform.OS === 'android' && timePickerMode === 'date') {
      setTimeout(() => {
        setTimePickerMode('time');
        setShowDatePicker(true);
      }, 100);
    }

    // iOS için veya saat seçildiğinde raporu güncelle
    if (Platform.OS === 'ios' || timePickerMode === 'time') {
      if (reportType === 'custom') {
        setTimeout(() => {
          fetchReportData();
        }, 100);
      }
    }
  };

  const formatDate = (date: Date): string => {
    return formatTurkishDate(date.toISOString());
  };

  const formatTime = (date: Date): string => {
    return formatTurkishTime(date.toISOString());
  };

  const formatDateTime = (date: Date): string => {
    return `${formatDate(date)} ${formatTime(date)}`;
  };

  const renderReportTypeButtons = () => {
    const reportTypes = [
      { id: 'today' as ReportType, label: 'Bugün', icon: 'today' },
      { id: 'yesterday' as ReportType, label: 'Dün', icon: 'event' },
      { id: 'this_month' as ReportType, label: 'Bu Ay', icon: 'date-range' },
      { id: 'last_month' as ReportType, label: 'Geçen Ay', icon: 'calendar-today' },
      { id: 'last_7days' as ReportType, label: 'Son 7 Gün', icon: 'view-week' },
      { id: 'custom' as ReportType, label: 'Özel Tarih', icon: 'date-range' }
    ];

    return (
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        style={styles.reportTypesContainer}
        scrollEventThrottle={16}
        decelerationRate="fast"
      >
        {reportTypes.map((type) => (
          <TouchableOpacity
            key={type.id}
            style={[
              styles.reportTypeButton,
              reportType === type.id && styles.selectedReportType
            ]}
            onPress={() => setReportType(type.id)}
          >
            <Icon
              name={type.icon}
              size={18}
              color={reportType === type.id ? '#fff' : '#333'}
            />
            <Text
              style={[
                styles.reportTypeText,
                reportType === type.id && styles.selectedReportTypeText
              ]}
            >
              {type.label}
            </Text>
          </TouchableOpacity>
        ))}
      </ScrollView>
    );
  };

  const renderSummaryCards = () => {
    if (!reportData) return null;

    return (
      <View style={styles.summaryContainer}>
        <View style={styles.summaryCardsContainer}>
          {renderSummaryItems()}
        </View>
      </View>
    );
  };

  const renderSummaryItems = () => {
    if (!reportData) return null;

    const summaryItems = [
      {
        title: 'Toplam Sipariş',
        value: reportData.ordersCount || 0,
        icon: 'receipt',
        color: '#4CAF50',
        onPress: fetchOrderDetails
      },
      {
        title: 'Ortalama Sipariş',
        value: formatCurrency(reportData.averageOrderValue || 0),
        icon: 'trending-up',
        color: '#9C27B0'
      },
      {
        title: 'Toplam Ciro',
        value: formatCurrency(reportData.revenueTotal || 0),
        icon: 'account-balance',
        color: '#FF9800'
      },
      {
        title: 'Net Gelir',
        value: formatCurrency(reportData.netRevenue || 0),
        icon: 'payments',
        color: '#2196F3'
      },
      {
        title: 'Toplam Vergi',
        value: formatCurrency(reportData.taxTotal || 0),
        icon: 'receipt-long',
        color: '#F44336'
      },
      {
        title: 'Toplam İndirim',
        value: formatCurrency(
          typeof reportData.getTotalDiscounts === 'object'
            ? reportData.getTotalDiscounts?.total || 0
            : reportData.getTotalDiscounts || 0
        ),
        icon: 'local-offer',
        color: '#E91E63',
        onPress: handleDiscountDetailsClick
      },
      {
        title: 'İptal Toplamı',
        value: formatCurrency(reportData.cancelledOrdersAndItems?.total || 0),
        icon: 'cancel',
        color: '#F44336',
        onPress: handleCancelledDetailsClick
      },
      {
        title: 'Ödenmez Toplamı',
        value: formatCurrency(
          typeof reportData.creditTotal === 'object'
            ? reportData.creditTotal?.total || 0
            : reportData.creditTotal || 0
        ),
        icon: 'credit-card',
        color: '#607D8B',
        onPress: handleCreditDetailsClick
      },
      {
        title: 'İkram Toplamı',
        value: formatCurrency(
          typeof reportData.complimentaryTotal === 'object'
            ? reportData.complimentaryTotal?.total || 0
            : reportData.complimentaryTotal || 0
        ),
        icon: 'card-giftcard',
        color: '#9C27B0',
        onPress: handleComplimentaryDetailsClick
      },
      {
        title: 'Zayi Toplamı',
        value: formatCurrency(
          typeof reportData.wasteTotal === 'object'
            ? reportData.wasteTotal?.total || 0
            : reportData.wasteTotal || 0
        ),
        icon: 'delete-outline',
        color: '#FF9800',
        onPress: handleWasteDetailsClick
      },
      {
        title: 'Açık Siparişler',
        value: formatCurrency(reportData.openOrdersTotal || calculateOpenOrdersTotal()),
        icon: 'pending-actions',
        color: '#FF9800',
        onPress: handleOpenOrdersClick
      },
      {
        title: 'Garson Performansı',
        value: `${(reportData as any).waiterSalesReport?.length || 0} Garson`,
        icon: 'people',
        color: '#9C27B0',
        onPress: handleWaiterPerformanceClick
      },
    ];

    return (
      <>
        {summaryItems.map((item, index) => (
          <TouchableOpacity
            key={index}
            style={styles.summaryCard}
            onPress={item.onPress}
            disabled={!item.onPress}
          >
            <View style={[styles.iconContainer, { backgroundColor: item.color }]}>
              <Icon name={item.icon} size={24} color="#fff" />
            </View>
            <View style={styles.summaryCardContent}>
              <Text style={styles.summaryCardTitle}>{item.title}</Text>
              <Text style={styles.summaryCardValue}>{item.value}</Text>
            </View>
          </TouchableOpacity>
        ))}
      </>
    );
  };

  const renderLastCashRegisterSessions = () => {
    if (loadingLastCashSessions) {
      return (
        <View style={styles.cashSessionsLoading}>
          <ActivityIndicator size="small" color="#FF9800" />
          <Text style={styles.cashSessionsLoadingText}>Son kasa oturumları yükleniyor...</Text>
        </View>
      );
    }

    const sessions = Array.isArray(lastCashSessions) ? lastCashSessions : [];

    if (sessions.length === 0) {
      return (
        <View style={styles.noCashSessions}>
          <Text style={styles.noCashSessionsText}>Son kasa oturumu bulunmuyor.</Text>
        </View>
      );
    }

    return (
      <View style={styles.cashSessionsContainer}>
        <Text style={styles.cashSessionsTitle}>Son Kasa Oturumları</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          scrollEventThrottle={16}
          decelerationRate="fast"
        >
          {sessions.map((session: any, index: number) => {
            if (!session) return null;

            const registerName = session.register_name || 'İsimsiz Kasa';
            const openedAt = session.opened_at || new Date().toISOString();
            const status = session.status || 'unknown';

            return (
              <TouchableOpacity
                key={index}
                style={[
                  styles.cashSessionCard,
                  status === 'open' ? styles.openSessionCard : styles.closedSessionCard
                ]}
                onPress={() => handleCashSessionClick(session)}
              >
                <View style={styles.cashSessionHeader}>
                  <Icon
                    name="point-of-sale"
                    size={20}
                    color={status === 'open' ? '#4CAF50' : '#FF9800'}
                  />
                  <Text style={styles.cashSessionName}>
                    {registerName} {status === 'open' ? '(Açık)' : '(Kapalı)'}
                  </Text>
                </View>

                {/* Lokasyon Bilgisi */}
                {session.register_location && (
                  <View style={styles.cashSessionLocation}>
                    <Icon name="location-on" size={14} color="#666" />
                    <Text style={styles.cashSessionLocationText}>
                      {session.register_location}
                    </Text>
                  </View>
                )}

                <View style={styles.cashSessionDetails}>
                  <View style={styles.cashSessionDetail}>
                    <Text style={styles.cashSessionDetailLabel}>Açılış Tutarı</Text>
                    <Text style={styles.cashSessionDetailValue}>
                      {formatCurrency(parseFloat(session.opening_amount || '0'))}
                    </Text>
                  </View>

                  {session.closing_amount !== null && session.closing_amount !== undefined && (
                    <View style={styles.cashSessionDetail}>
                      <Text style={styles.cashSessionDetailLabel}>Kapanış Tutarı</Text>
                      <Text style={styles.cashSessionDetailValue}>
                        {formatCurrency(parseFloat(session.closing_amount))}
                      </Text>
                    </View>
                  )}

                  <View style={styles.cashSessionDetail}>
                    <Text style={styles.cashSessionDetailLabel}>Kasa Kalan</Text>
                    <Text style={styles.cashSessionDetailValue}>
                      {formatCurrency(parseFloat(session.total_transactions || '0'))}
                    </Text>
                  </View>

                  {session.expected_amount !== null && session.expected_amount !== undefined && (
                    <View style={styles.cashSessionDetail}>
                      <Text style={styles.cashSessionDetailLabel}>Beklenen Tutar</Text>
                      <Text style={styles.cashSessionDetailValue}>
                        {formatCurrency(parseFloat(session.expected_amount))}
                      </Text>
                    </View>
                  )}

                  {session.difference_amount !== null && session.difference_amount !== undefined && (
                    <View style={styles.cashSessionDetail}>
                      <Text style={styles.cashSessionDetailLabel}>Fark</Text>
                      <Text style={[
                        styles.cashSessionDetailValue,
                        { color: parseFloat(session.difference_amount) >= 0 ? '#4CAF50' : '#F44336' }
                      ]}>
                        {parseFloat(session.difference_amount) >= 0 ? '+' : ''}
                        {formatCurrency(parseFloat(session.difference_amount))}
                      </Text>
                    </View>
                  )}

                  <View style={styles.cashSessionDetail}>
                    <Text style={styles.cashSessionDetailLabel}>Para Çıkışı</Text>
                    <Text style={styles.cashSessionDetailValue}>
                      {formatCurrency(parseFloat(session.total_withdrawals || '0'))} ({session.withdrawal_count || 0})
                    </Text>
                  </View>

                  <View style={styles.cashSessionDetail}>
                    <Text style={styles.cashSessionDetailLabel}>Para Girişi</Text>
                    <Text style={styles.cashSessionDetailValue}>
                      {formatCurrency(parseFloat(session.total_deposits || '0'))} ({session.deposit_count || 0})
                    </Text>
                  </View>
                </View>

                {/* Ödeme Türleri */}
                {session.payment_totals_by_type && session.payment_totals_by_type.length > 0 && (
                  <View style={styles.cashSessionPayments}>
                    <Text style={styles.cashSessionPaymentsTitle}>Ödeme Türleri:</Text>
                    {session.payment_totals_by_type.map((payment: any, paymentIndex: number) => (
                      <View key={paymentIndex} style={styles.cashSessionPaymentRow}>
                        <Text style={styles.cashSessionPaymentName}>{payment.payment_type_name}:</Text>
                        <Text style={styles.cashSessionPaymentAmount}>
                          {formatCurrency(parseFloat(payment.total_amount))}
                        </Text>
                      </View>
                    ))}

                    {/* Ödeme Türleri Toplamı */}
                    <View style={styles.cashSessionPaymentTotalRow}>
                      <Text style={styles.cashSessionPaymentTotalLabel}>TOPLAM:</Text>
                      <Text style={styles.cashSessionPaymentTotalAmount}>
                        {formatCurrency(
                          session.payment_totals_by_type.reduce((sum: number, payment: any) => {
                            return sum + parseFloat(payment.total_amount || '0');
                          }, 0)
                        )}
                      </Text>
                    </View>
                  </View>
                )}

                {/* Kişi ve Zaman Bilgileri */}
                <View style={styles.cashSessionFooter}>
                  <View style={styles.cashSessionPeople}>
                    <View style={styles.cashSessionPersonRow}>
                      <Icon name="person" size={14} color="#666" />
                      <Text style={styles.cashSessionPerson}>
                        Açan: {session.opener_name || 'Bilinmiyor'}
                      </Text>
                    </View>
                    {session.closer_name && (
                      <View style={styles.cashSessionPersonRow}>
                        <Icon name="person" size={14} color="#666" />
                        <Text style={styles.cashSessionPerson}>
                          Kapatan: {session.closer_name}
                        </Text>
                      </View>
                    )}
                  </View>

                  <View style={styles.cashSessionTimes}>
                    <View style={styles.cashSessionTimeRow}>
                      <Icon name="access-time" size={14} color="#666" />
                      <Text style={styles.cashSessionTime}>
                        Açılış: {formatTurkishDateTime(session.opened_at)}
                      </Text>
                    </View>
                    {session.closed_at && (
                      <View style={styles.cashSessionTimeRow}>
                        <Icon name="access-time" size={14} color="#666" />
                        <Text style={styles.cashSessionTime}>
                          Kapanış: {formatTurkishDateTime(session.closed_at)}
                        </Text>
                      </View>
                    )}
                  </View>
                </View>
              </TouchableOpacity>
            );
          })}
        </ScrollView>
      </View>
    );
  };

  const renderPaymentTypes = () => {
    if (!reportData?.totalPaymentsByPaymentTypes || reportData.totalPaymentsByPaymentTypes.length === 0) {
      return null;
    }

    // Toplam hesapla
    const totalAmount = reportData.totalPaymentsByPaymentTypes.reduce((sum, payment) => {
      return sum + parseFloat(String(payment.total || '0'));
    }, 0);

    return (
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Ödeme Türleri</Text>
        <View style={styles.paymentTypesContainer}>
          {reportData.totalPaymentsByPaymentTypes.map((payment, index) => (
            <View key={index} style={styles.paymentTypeItem}>
              <View style={styles.paymentTypeInfo}>
                <Icon name="payment" size={20} color="#4CAF50" />
                <Text style={styles.paymentTypeName}>{payment.title}</Text>
              </View>
              <Text style={styles.paymentTypeAmount}>{formatCurrency(payment.total)}</Text>
            </View>
          ))}

          {/* Toplam Satırı */}
          <View style={styles.paymentTypeTotalRow}>
            <View style={styles.paymentTypeInfo}>
              <Icon name="calculate" size={20} color="#2196F3" />
              <Text style={styles.paymentTypeTotalLabel}>TOPLAM</Text>
            </View>
            <Text style={styles.paymentTypeTotalAmount}>{formatCurrency(totalAmount)}</Text>
          </View>
        </View>
      </View>
    );
  };

  const renderTopSellingItems = () => {
    if (!reportData?.topSellingItems || reportData.topSellingItems.length === 0) {
      return null;
    }

    return (
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Satılan Ürünler</Text>
        <View style={styles.topItemsContainer}>
          {reportData.topSellingItems.slice(0, 1000).map((item, index) => (
            <View key={index} style={styles.topSellingItem}>
              <View style={styles.topSellingRank}>
                <Text style={styles.rankNumber}>{index + 1}</Text>
              </View>
              <View style={styles.topSellingInfo}>
                <Text style={styles.topSellingName}>{item.title}</Text>
                <Text style={styles.topSellingDetails}>
                  {item.orders_count} sipariş • {formatCurrency(item.price)}
                </Text>
              </View>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderFloorReports = () => {
    if (!reportData?.netRevenueByFloor || reportData.netRevenueByFloor.length === 0) {
      return null;
    }

    return (
      <View style={styles.sectionContainer}>
        <Text style={styles.sectionTitle}>Alan Bazlı Rapor</Text>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          style={styles.horizontalScrollContainer}
          contentContainerStyle={styles.horizontalScrollContent}
          scrollEventThrottle={16}
          decelerationRate="fast"
        >
          {reportData.netRevenueByFloor.map((floor, index) => (
            <View key={index} style={styles.areaCard}>
              <View style={styles.areaHeader}>
                <Icon name="business" size={20} color="#2196F3" />
                <Text style={styles.areaName}>
                  {floor.floor_title}
                </Text>
              </View>

              <View style={styles.areaDetails}>
                <View style={styles.areaDetail}>
                  <Text style={styles.areaDetailLabel}>Net Gelir</Text>
                  <Text style={styles.areaDetailValue}>
                    {formatCurrency(parseFloat(floor.net_revenue))}
                  </Text>
                </View>

                <View style={styles.areaDetail}>
                  <Text style={styles.areaDetailLabel}>Sipariş Sayısı</Text>
                  <Text style={styles.areaDetailValue}>
                    {floor.order_count} sipariş
                  </Text>
                </View>

                <View style={styles.areaDetail}>
                  <Text style={styles.areaDetailLabel}>Ortalama Sipariş</Text>
                  <Text style={styles.areaDetailValue}>
                    {formatCurrency(parseFloat(floor.avg_order_value))}
                  </Text>
                </View>
              </View>
            </View>
          ))}
        </ScrollView>
      </View>
    );
  };



  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <View style={styles.headerRow}>
          <TouchableOpacity
            style={styles.backButton}
            onPress={() => navigation.replace('Dashboard')}
          >
            <Icon name="arrow-back" size={24} color="#333" />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Raporlar</Text>
        </View>
        {renderReportTypeButtons()}
      </View>

      {reportType === 'custom' && (
        <View style={styles.datePickerContainer}>
          <View style={styles.dateRow}>
            <Text style={styles.dateLabel}>Başlangıç:</Text>
            <TouchableOpacity
              style={styles.dateButton}
              onPress={showStartDatePicker}
            >
              <Text style={styles.dateButtonText}>{formatDate(startDate)}</Text>
              <Icon name="calendar-today" size={16} color="#2196F3" />
            </TouchableOpacity>
          </View>

          <View style={styles.dateRow}>
            <Text style={styles.dateLabel}>Bitiş:</Text>
            <TouchableOpacity
              style={styles.dateButton}
              onPress={showEndDatePicker}
            >
              <Text style={styles.dateButtonText}>{formatDate(endDate)}</Text>
              <Icon name="calendar-today" size={16} color="#2196F3" />
            </TouchableOpacity>
          </View>
        </View>
      )}

      {showDatePicker && (
        <DateTimePicker
          value={datePickerMode === 'start' ? startDate : endDate}
           mode={timePickerMode}

          display="default"
          onChange={onDateChange}
        />
      )}

      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2196F3" />
          <Text style={styles.loadingText}>Raporlar yükleniyor...</Text>
        </View>
      ) : error ? (
        <View style={styles.errorContainer}>
          <Icon name="error-outline" size={60} color="#F44336" />
          <Text style={styles.errorText}>{error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={fetchReportData}>
            <Text style={styles.retryButtonText}>Tekrar Dene</Text>
          </TouchableOpacity>
        </View>
      ) : (
        <ScrollView
          style={styles.scrollContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
          scrollEventThrottle={16}
        >
          {renderSummaryCards()}
          {renderPaymentTypes()}
          {renderFloorReports()}
          {renderLastCashRegisterSessions()}
          {renderTopSellingItems()}
        </ScrollView>
      )}

    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#fff',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
  },
  backButton: {
    padding: 5,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
    textAlign: 'left',
  },
  reportTypesContainer: {
    backgroundColor: '#fff',
    paddingVertical: 10,
    paddingHorizontal: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    height: 60,
  },
  reportTypeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    marginRight: 10,
    height: 40,
  },
  selectedReportType: {
    backgroundColor: '#2196F3',
  },
  reportTypeText: {
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 5,
    color: '#333',
  },
  selectedReportTypeText: {
    color: '#fff',
  },
  datePickerContainer: {
    backgroundColor: '#fff',
    padding: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
  },
  dateRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10,
  },
  dateLabel: {
    fontSize: 16,
    color: '#333',
    fontWeight: '500',
  },
  dateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 5,
  },
  dateButtonText: {
    fontSize: 14,
    color: '#333',
    marginRight: 8,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginTop: 10,
    marginBottom: 20,
  },
  retryButton: {
    backgroundColor: '#2196F3',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 5,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: 'bold',
  },
  scrollContainer: {
    flex: 1,
  },
  summaryContainer: {
    paddingTop: 15
  },
  summaryCardsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 10,
    justifyContent: 'space-between',
  },
  summaryCard: {
    width: '48%',
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginBottom: 10,
    flexDirection: 'row',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 10,
  },
  summaryCardContent: {
    flex: 1,
  },
  summaryCardTitle: {
    fontSize: 12,
    color: '#666',
    marginBottom: 5,
  },
  summaryCardValue: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
  },
  cashSessionsContainer: {
    marginBottom: 20,
  },
  cashSessionsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    marginLeft: 10,
    marginTop: 10,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingBottom: 8,
  },
  cashSessionsLoading: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 10,
  },
  cashSessionsLoadingText: {
    marginLeft: 10,
    fontSize: 14,
    color: '#666',
  },
  noCashSessions: {
    padding: 20,
    backgroundColor: '#fff',
    borderRadius: 10,
    margin: 10,
    alignItems: 'center',
  },
  noCashSessionsText: {
    fontSize: 14,
    color: '#666',
  },
  cashSessionCard: {
    width: 300,
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginHorizontal: 5,
    marginLeft: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  cashSessionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  cashSessionName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  cashSessionDetails: {
    marginBottom: 15,
  },
  cashSessionDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  cashSessionDetailLabel: {
    fontSize: 12,
    color: '#666',
  },
  cashSessionDetailValue: {
    fontSize: 12,
    color: '#333',
  },
  openSessionCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  closedSessionCard: {
    borderLeftWidth: 4,
    borderLeftColor: '#FF9800',
  },
  sectionContainer: {
    backgroundColor: '#fff',
    margin: 10,
    borderRadius: 10,
    padding: 15,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    paddingBottom: 8,
  },
  paymentTypesContainer: {
    marginTop: 10,
  },
  paymentTypeItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  paymentTypeInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  paymentTypeName: {
    fontSize: 16,
    color: '#333',
    marginLeft: 10,
  },
  paymentTypeAmount: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  paymentTypeTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    marginTop: 8,
    borderTopWidth: 2,
    borderTopColor: '#2196F3',
    backgroundColor: '#f8f9fa',
  },
  paymentTypeTotalLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
    marginLeft: 10,
  },
  paymentTypeTotalAmount: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  topItemsContainer: {
    marginTop: 10,
  },
  topSellingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  topSellingRank: {
    width: 30,
    height: 30,
    borderRadius: 15,
    backgroundColor: '#2196F3',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  rankNumber: {
    color: '#fff',
    fontWeight: 'bold',
    fontSize: 14,
  },
  topSellingInfo: {
    flex: 1,
  },
  topSellingName: {
    fontSize: 16,
    fontWeight: '500',
    color: '#333',
    marginBottom: 4,
  },
  topSellingDetails: {
    fontSize: 14,
    color: '#666',
  },
  // Yatay Scroll Styles
  horizontalScrollContainer: {
    marginTop: 10,
  },
  horizontalScrollContent: {
    paddingHorizontal: 10,
  },

  // Alan Bazlı Rapor Styles (Kasa Oturumu Tarzında)
  areaCard: {
    width: 300,
    backgroundColor: '#fff',
    borderRadius: 10,
    padding: 15,
    marginRight: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1.5,
    elevation: 2,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  areaHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  areaName: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginLeft: 8,
  },
  areaDetails: {
    marginBottom: 15,
  },
  areaDetail: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 5,
  },
  areaDetailLabel: {
    fontSize: 12,
    color: '#666',
  },
  areaDetailValue: {
    fontSize: 12,
    color: '#333',
    fontWeight: '500',
  },
  areaFooter: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
    alignItems: 'center',
  },
  areaFooterText: {
    fontSize: 11,
    color: '#999',
    fontStyle: 'italic',
  },
  actionButtonContainer: {
    padding: 15,
  },
  actionButton: {
    backgroundColor: '#2196F3',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 15,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
    elevation: 3,
  },
  actionButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
    marginLeft: 8,
  },

  // Kasa Oturumu Ek Style'ları
  cashSessionLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    paddingHorizontal: 4,
  },
  cashSessionLocationText: {
    fontSize: 12,
    color: '#666',
    marginLeft: 4,
    fontStyle: 'italic',
  },
  cashSessionPayments: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  cashSessionPaymentsTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 8,
  },
  cashSessionPaymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 4,
  },
  cashSessionPaymentName: {
    fontSize: 11,
    color: '#666',
  },
  cashSessionPaymentAmount: {
    fontSize: 11,
    fontWeight: 'bold',
    color: '#4CAF50',
  },
  cashSessionPaymentTotalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 8,
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#e0e0e0',
  },
  cashSessionPaymentTotalLabel: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  cashSessionPaymentTotalAmount: {
    fontSize: 12,
    fontWeight: 'bold',
    color: '#2196F3',
  },
  cashSessionFooter: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  cashSessionPeople: {
    marginBottom: 8,
  },
  cashSessionPerson: {
    fontSize: 11,
    color: '#666',
    marginBottom: 2,
  },
  cashSessionTimes: {
    gap: 2,
  },
  cashSessionTime: {
    fontSize: 10,
    color: '#999',
  },
  cashSessionPersonRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 2,
  },
  cashSessionTimeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 2,
  },
});

export default ReportsScreen;